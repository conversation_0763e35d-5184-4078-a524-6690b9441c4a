{"name": "admin_vue3_element_plus", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --open", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tinymce/tinymce-vue": "^6.0.1", "@vueuse/core": "^10.7.0", "@vueuse/integrations": "^10.7.0", "axios": "^1.6.2", "echarts": "^5.4.3", "element-plus": "^2.4.3", "gsap": "^3.12.4", "nprogress": "^0.2.0", "tinymce": "^7.3.0", "universal-cookie": "^6.1.1", "vue": "^3.3.8", "vue-router": "^4.2.5", "vuex": "^4.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "sass": "^1.69.5", "vite": "^5.0.0", "vite-plugin-windicss": "^1.9.2", "windicss": "^3.5.6"}}