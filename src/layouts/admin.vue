<!--
 * @Author: yangy
 * @Date: 2023-12-19 10:56:19
 * @LastEditors: yangy
 * @LastEditTime: 2023-12-25 11:38:05
 * @FilePath: /admin_vue3_element_plus/src/layouts/admin.vue
 * @Description: 
 * 
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved. 
-->
<template>
    <el-container>
      <el-header>
        <!-- 头部 通栏 -->
        <f-header />
      </el-header> 
      <el-container>
        <!-- 侧边栏 -->
        <el-aside :width="$store.state.asideWidth">
          <f-menu />
        </el-aside>
        <el-main>
          <!-- 主体 -->
          <f-tag-list />
          <router-view v-slot="{ Component }">
            <!-- 动画 -->
            <!-- 使用transition 的时候只能由一个跟节点 -->
            <transition name="fade">
              <keep-alive :max="10">
              <component :is="Component"></component>
            </keep-alive>
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>
</template>

<script setup>
import FHeader from './components/FHeader.vue'
import FMenu from './components/FMenu.vue'
import FTagList from './components/FTagList.vue'
</script>

<style>
  .el-aside{
    transition: all 0.2s;
  }

  .fade-enter-from{
    opacity: 0;
  }

  .fade-enter-to{
    opacity: 1;
  }

  .fade-leave-from{
    opacity: 1;
  }

  .fade-leave-to{
    opacity: 0;
  }

  .fade-enter-active,.fade-leave-active{
    transition: all 0.3s;
  }
  .fade-enter-active{
    transition-delay: 0.3s;
  }
</style>