<!--
 * @Author: yangy
 * @Date: 2023-12-19 11:02:13
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-02-29 14:31:21
 * @FilePath: /admin_vue3_element_plus/src/layouts/components/FMenu.vue
 * @Description: 
 * 
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved. 
-->
<template>
  <div class="f-menu" :style="{ width: $store.state.asideWidth }">
    <!-- unique-opened:保持只有一个展开 -->
    <el-menu
      unique-opened
      :collapse="isCollapse"
      :default-active="defaultActive"
      class="border-0"
      @select="handleSelect"
    >
      <template v-for="(item, index) in asideMenus" :key="index">
        <el-sub-menu
          v-if="item.child && item.child.length > 0"
          :index="item.name"
        >
          <template #title>
            <el-icon>
              <component :is="item.icon"></component>
            </el-icon>
            <span>{{ item.name }}</span>
          </template>
          <el-menu-item
            v-for="(item2, index2) in item.child"
            :key="index2"
            :index="item2.frontpath"
          >
            <el-icon>
              <component :is="item2.icon"></component>
            </el-icon>
            <span>{{ item2.name }}</span>
          </el-menu-item>
        </el-sub-menu>
        <!--  -->
        <el-menu-item v-else :index="item.frontpath">
          <el-icon>
            <component :is="item.icon"></component>
          </el-icon>
          <span>{{ item.name }}</span>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<script setup>
import { useRouter, useRoute, onBeforeRouteUpdate } from "vue-router";
import { computed, ref } from "vue";
import { useStore } from "vuex";
const router = useRouter();
const store = useStore();
const route = useRoute();

// 默认选中  刷新还是定位到本页面

const defaultActive = ref(route.path);
//监听路由变化
// 标签导航和菜单联动
// vue-router的组件内的守卫
// 路由发生变化的时候触发:beforeRouteUpdate
onBeforeRouteUpdate((to, from) => {
  defaultActive.value = to.path;
});

const isCollapse = computed(() => !(store.state.asideWidth == "250px"));
// 从存储中获取菜单
const asideMenus = computed(() => store.state.menus);

const handleSelect = (e) => {
  console.log(e);
  router.push(e);
};
</script>

<style>
.f-menu {
  transition: all 0.2s;
  top: 64px;
  bottom: 0;
  left: 0;
  /* overflow:auto; 如果超出了则加滚动条*/
  overflow-y: auto;
  overflow-x: hidden;
  @apply shadow-md fixed bg-light-50;
}

/* 隐藏侧边栏的滚动条 */
.f-menu::-webkit-scrollbar {
  width: 0px;
}
</style>
