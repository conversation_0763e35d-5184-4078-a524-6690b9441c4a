<!--
 * @Author: yangy
 * @Date: 2023-12-19 11:02:27
 * @LastEditors: yangy
 * @LastEditTime: 2023-12-22 12:50:34
 * @FilePath: /admin_vue3_element_plus/src/layouts/components/FTagList.vue
 * @Description: 
 * 
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved. 
-->
<template>
  <div class="f-tag-list" :style="{ left:$store.state.asideWidth }">
    <el-tabs
    v-model="activeTab"
    type="card"
    class="flex-1"
    @tab-remove="removeTab"
    style="min-width:100px"
    @tab-change="changeTab"
  >
  <!-- :closable 的作用是现实关闭按钮,下面的判断是后台首页不显示关闭按钮 -->
    <el-tab-pane
      :closable = "item.path != '/'" 
      v-for="item in tabList"
      :key="item.path"
      :label="item.title"
      :name="item.path"
    >
    </el-tab-pane>
  </el-tabs>
  <span class="tag-btn">
    <el-dropdown @command="handleClose">
      <span class="el-dropdown-link">
         <el-icon>
          <arrow-down />
        </el-icon>
      </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="clearOther">关闭其他</el-dropdown-item>
          <el-dropdown-item command="clearAll">全部关闭</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </span>
  </div>
  <div style="height: 44px;"></div>
</template>

<script setup>
import {useTabList} from "@/utils/useTabList"
const {activeTab, tabList, changeTab, removeTab, handleClose} = useTabList()
</script>

<style scoped>
.f-tag-list{
  @apply fixed bg-gray-100 flex items-center px-2;
  top:64px;
  right:0;
  height: 44px;
  z-index: 100;
}

.tag-btn{
  @apply bg-white rounded ml-auto flex items-center justify-center px-2;
  height: 32px;
}
:deep(.el-tabs__header){
  @apply mb-0;
  border: 0!important;
}
:deep(.el-tabs__nav){
  border: 0!important;
}
:deep(.el-tabs__item){
  border: 0!important;
  height: 36px;
  line-height: 36px;
  @apply bg-white mx-1 rounded;
  margin-top: 2px;
}
:deep(.el-tabs__nav-next),:deep(.el-tabs__nav-prev){
  line-height: 32px;
  height: 32px;
}
:deep(.is-disabled){
  cursor:not-allowed;
  @apply text-gray-300
}
</style>