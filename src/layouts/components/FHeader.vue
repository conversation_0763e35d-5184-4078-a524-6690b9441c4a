<!--
 * @Author: yangy
 * @Date: 2023-12-19 11:02:00
 * @LastEditors: yangy
 * @LastEditTime: 2023-12-26 12:38:40
 * @FilePath: /admin_vue3_element_plus/src/layouts/components/FHeader.vue
 * @Description: 
 * 
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved. 
-->
<template>
  <div class="f-header">
    <span class="logo">
      <el-icon class="mr-1"><eleme-filled /></el-icon>
      青柠商城
    </span>
    <el-icon class="icon-btn" @click="$store.commit('handleAsideWidth')">
      <fold v-if= "$store.state.asideWidth == '250px' " />
      <Expand v-else />
    </el-icon>
    <!-- 提示 -->
    <el-tooltip effect="dark" content="刷新" placement="bottom">
      <el-icon class="icon-btn" @click="handleRefresh"><refresh /></el-icon>
    </el-tooltip>
    <div class="ml-auto flex items-center">
      <!-- 提示 -->
      <el-tooltip effect="dark" content="全屏" placement="bottom"
        ><el-icon class="icon-btn" @click="toggle">
          <full-screen v-if="!isFullscreen" />
          <aim v-else /> </el-icon
      ></el-tooltip>

      <!--  command 是下拉菜单的监听事件 -->
      <el-dropdown class="dropdown" @command="handelCommand">
        <span class="flex items-center text-light-50">
          <el-avatar
            class="mr-2"
            :size="25"
            :src="$store.state.user.data.avatar"
          />
          {{ $store.state.user.data.username }}
          <el-icon class="el-icon--right">
            <arrow-down />
          </el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="rePassword">修改密码</el-dropdown-item>
            <el-dropdown-item command="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
  <!-- 修改密码 抽屉组件 -->
  <form-drawer
    ref="formDrawerRef"
    title="修改密码"
    destroyOnClose
    @submit="onSubmit"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="80px"
      size="small"
    >
      <el-form-item prop="oldpassword" label="旧 密 码">
        <el-input
          v-model="form.oldpassword"
          type="password"
          placeholder="请输入旧密码"
        >
        </el-input>
      </el-form-item>
      <el-form-item prop="password" label="新 密 码">
        <el-input
          v-model="form.password"
          type="password"
          show-password
          placeholder="请输入新密码"
        >
        </el-input>
      </el-form-item>
      <el-form-item prop="repassword" label="确认密码">
        <el-input
          v-model="form.repassword"
          type="password"
          show-password
          placeholder="请确认新密码"
        >
        </el-input>
      </el-form-item>
    </el-form>
  </form-drawer>
</template>

<script setup>
import FormDrawer from "@/components/FormDrawer.vue";
//  全屏
import { useFullscreen } from "@vueuse/core";
import { useRePassword, useLogout } from "@/utils/useManager";

// 是否全屏状态:isFullscreen , 切换:toggle
const { isFullscreen, toggle } = useFullscreen();

const { formDrawerRef, form, rules, formRef, onSubmit, openRePasswordForm } =
  useRePassword();
const { handleLogout } = useLogout();

// const formDrawerRef = ref(false)
// const router = useRouter();
// const store = useStore();
// 修改密码部分 抽屉组件
// const showDrawer = ref(false)

//  下拉组件监听事件 或触发事件
const handelCommand = (c) => {
  switch (c) {
    case "logout":
      handleLogout();
      break;
    case "rePassword":
      openRePasswordForm();
      break;
    default:
      break;
  }
};

// 刷新
const handleRefresh = () => {
  location.reload();
};
</script>

<style>
.f-header {
  @apply flex items-center bg-indigo-700 text-light-50 fixed top-0 left-0 right-0;
  height: 64px;
  z-index: 1000;
}

.logo {
  width: 250px;
  @apply flex justify-center items-center text-xl font-thin;
}
.icon-btn {
  @apply flex justify-center items-center;
  width: 42px;
  height: 64px;
  cursor: pointer;
}
.icon-btn:hover {
  @apply bg-indigo-600;
}

.f-header .dropdown {
  width: 64px;
  cursor: pointer;
  @apply flex justify-center items-center mx-5;
}
</style>
