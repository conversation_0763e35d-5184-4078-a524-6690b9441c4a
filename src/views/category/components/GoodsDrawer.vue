<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-05-07 15:21:07
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-05-07 17:34:58
 * @FilePath: /admin_vue3_element_plus/src/views/category/components/GoodsDrawer.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <FormDrawer
    ref="formDrawerRef"
    title="推荐商品"
    @submit="handleConnect"
    confirmText="关联"
  >
    <el-table :data="tableData" border stripe style="100%">
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column label="商品封面" width="180">
        <template #default="{ row }">
          <el-image
            :src="row.cover"
            :lazy="true"
            style="width: 64px; height: 64px"
            fit="fill"
          ></el-image>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="商品名称" width="180" />
      <el-table-column label="操作" width="180">
        <template #default="{ row }">
          <el-popconfirm
            title="是否要删除该记录?"
            confirmButtonText="确认"
            cancelButtonText="取消"
            @confirm="handleDelete(row)"
          >
            <template #reference>
              <el-button type="primary" text size="small" :loading="row.loading"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </FormDrawer>
  <ChooseGoods ref="ChooseGoodsRef" />
</template>

<script setup>
import { ref } from "vue";
import FormDrawer from "@/components/FormDrawer.vue";
import ChooseGoods from "@/components/ChooseGoods.vue";
import {
  getCategoryGoods,
  deleteCategoryGoods,
  connectCategoryGoods,
} from "@/api/category.js";
import { Eptoast } from "@/utils/util.js";
const formDrawerRef = ref(null);
const tableData = ref([]);
const category_id = ref(null);
const ChooseGoodsRef = ref(null);

const open = (item) => {
  item.goodsDrawerLoading = true;
  category_id.value = item.id;
  getData()
    .then((res) => {
      formDrawerRef.value.open();
    })
    .finally(() => {
      item.goodsDrawerLoading = false;
    });
};

function getData() {
  return getCategoryGoods(category_id.value).then((res) => {
    console.log(res);
    tableData.value = res.data.map((o) => {
      o.loading = false;
      return o;
    });
  });
}

const handleDelete = (row) => {
  row.loading = true;
  deleteCategoryGoods(row.id).then((res) => {
    Eptoast("删除成功");
    getData();
  });
};

const handleConnect = () => {
  ChooseGoodsRef.value.open((goods_ids) => {
    console.log(goods_ids);
    formDrawerRef.value.showLoading();
    connectCategoryGoods({
      category_id: category_id.value,
      goods_ids,
    })
      .then((res) => {
        getData();
        Eptoast("关联成功");
      })
      .finally(() => {
        formDrawerRef.value.hideLoading();
      });
  });
};
defineExpose({
  open,
});
</script>
