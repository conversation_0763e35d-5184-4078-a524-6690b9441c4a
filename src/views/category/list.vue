<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-28 15:31:47
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-05-07 16:06:00
 * @FilePath: /admin_vue3_element_plus/src/views/access/list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-card shadow="never" class="border-0">
      <ListHeader @create="handleCreate" @refresh="getData" />
      <!-- default-expanded-keys 默认展开一级菜单 -->
      <!-- @node-click="handleNodeClick" -->
      <el-tree
        :data="tableData"
        :props="defaultProps"
        v-loading="loading"
        node-key="id"
      >
        <template #default="{ data }">
          <div class="custom-tree-node">
            <span>{{ data.name }}</span>
            <div class="ml-auto">
              <el-button
                type="primary"
                size="small"
                text
                @click="openGoodsDrawer(data)"
                :loading="data.goodsDrawerLoading"
                >推荐商品</el-button
              >
              <el-switch
                :modelValue="data.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange($event, data)"
              />
              <el-button
                type="primary"
                size="small"
                text
                @click.stop="handleEdit(data)"
                >修改</el-button
              >

              <span @click.stop="() => {}">
                <el-popconfirm
                  title="是否要删除该记录?"
                  confirm-button-text="确认"
                  cancel-button-text="取消"
                  @confirm.stop="handleDelete(data.id)"
                >
                  <template #reference>
                    <el-button type="primary" size="small" text>删除</el-button>
                  </template>
                </el-popconfirm>
              </span>
            </div>
          </div>
        </template>
      </el-tree>

      <FormDrawer
        ref="formDrawerRef"
        :title="drawerTitle"
        @submit="handleSubmit"
      >
        <el-form
          :model="form"
          ref="formRef"
          :rules="rules"
          label-width="80px"
          :inline="false"
        >
          <!--emitPath: 在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值 -->
          <!-- checkStrictly 是否严格的遵守父子节点不互相关联 -->
          <!-- value  这个value 需要指定id 否则在切换选择的时候为空 -->
          <el-form-item label="分类名称" prop="name">
            <el-input v-model="form.name" placeholder="名称"></el-input>
          </el-form-item>
        </el-form>
      </FormDrawer>

      <GoodsDrawer ref="GoodsDrawerRef" />
    </el-card>
  </div>
</template>

<script setup>
import { ref } from "vue";
import ListHeader from "@/components/ListHeader.vue";
import FormDrawer from "@/components/FormDrawer.vue";
import GoodsDrawer from "./components/GoodsDrawer.vue";
import {
  getCategoryList,
  createCategory,
  updateCategory,
  updateCategoryStatus,
  deleteCategory,
  getCategoryGoods,
} from "@/api/category.js";
import { useInitTable, useInitForm } from "@/utils/useCommon.js";

const { loading, tableData, getData, handleDelete, handleStatusChange } =
  useInitTable({
    getList: getCategoryList,
    onGetListSuccess: (res) => {
      tableData.value = res.data.map((o) => {
        o.goodsDrawerLoading = false;
        return o;
      });
    },
    delete: deleteCategory,
    updateStatus: updateCategoryStatus,
  });

const {
  formDrawerRef,
  formRef,
  form,
  rules,
  editId,
  drawerTitle,
  handleSubmit,
  resetForm,
  handleCreate,
  handleEdit,
} = useInitForm({
  form: {
    name: "",
  },
  rules: {},
  getData,
  update: updateCategory,
  create: createCategory,
});

const GoodsDrawerRef = ref(null);
const openGoodsDrawer = (data) => GoodsDrawerRef.value.open(data);

const defaultProps = {
  label: "name",
  children: "child",
};
</script>

<style lang="scss" scoped>
.custom-tree-node {
  display: flex;
  flex: 1;
  align-items: center;
  font-size: 14px;
  padding-right: 8px;
}
:deep(.el-tree-node__content) {
  padding: 15px 0;
}
</style>
