<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-08-01 15:40:15
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-21 11:48:11
 * @FilePath: /admin_vue3_element_plus/src/views/goods/banners.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <FormDrawer
    ref="formDrawerRef"
    title="设置商品详情"
    @submit="submit"
    destroy-on-close
  >
    <el-form :model="state" ref="form" :inline="false">
      <el-form-item>
        <Editor v-model="state.content" :init="editorInit" />
        <!-- <el-input type="textarea" v-model="state.content" /> -->
      </el-form-item>
    </el-form>
  </FormDrawer>
</template>

<script setup>
import { ref, reactive } from "vue";
import FormDrawer from "@/components/FormDrawer.vue";
import { readGoods, updateGoods } from "@/api/goods.js";
import { Eptoast } from "@/utils/util";
import Editor from "@/components/Editor.vue";

const editorInit = {
  height: 300,
  menubar: false,
  plugins: ["lists link image preview"],
  toolbar:
    "template | undo redo | styles | bold italic | alignleft aligncenter alignright | bullist numlist outdent indent | link image",
  // 其他配置...
};

const formDrawerRef = ref(null);
const state = reactive({ content: "" });

// 打开这个抽屉
const goodsId = ref(0);
const open = (row) => {
  goodsId.value = row.id;
  row.contentLoaded = true;
  readGoods(goodsId.value)
    .then((res) => {
      state.content = res.data.content;
      formDrawerRef.value.open();
    })
    .finally(() => {
      row.contentLoaded = false;
    });
};
const emit = defineEmits(["reloadData"]);
const submit = () => {
  formDrawerRef.value.showLoading();
  updateGoods(goodsId.value, state)
    .then((res) => {
      Eptoast("设置商品详情成功");
      formDrawerRef.value.close();
      emit("reloadData");
    })
    .finally(() => {
      formDrawerRef.value.hideLoading();
    });
};

defineExpose({
  open,
});
</script>

<style scoped lang="scss"></style>
