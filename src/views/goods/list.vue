<!--
 * @Author: yangy <EMAIL>
 * @Date: 2023-12-25 16:35:42
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-28 10:29:00
 * @FilePath: /admin_vue3_element_plus/src/views/notice/list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-tabs v-model="searchForm.tab" tab-position="top" @tab-change="getData">
      <el-tab-pane
        v-for="(item, index) in tabbars"
        :key="index"
        :label="item.name"
        :name="item.key"
      >
      </el-tab-pane>
    </el-tabs>

    <el-card shadow="never" class="border-0">
      <!-- 搜索部分 -->
      <!-- search 被触发之后调用getData函数 -->
      <Search :model="searchForm" @search="getData" @reset="resetSearchForm">
        <SearchItem label="关键词">
          <el-input
            v-model="searchForm.title"
            placeholder="商品名称"
            clearable
          ></el-input>
        </SearchItem>
        <template #show>
          <SearchItem label="商品分类">
            <el-select
              v-model="searchForm.category_id"
              placeholder="请选择商品分类"
              clearable
              filterable
            >
              <el-option
                v-for="item in category_list"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </SearchItem>
        </template>
      </Search>
      <!-- 新增|刷新 -->
      <ListHeader
        layout="create,refresh"
        @create="handleCreate"
        @refresh="getData"
      >
        <el-button
          type="danger"
          size="small"
          @click="handleMultiDelete(1)"
          v-if="searchForm.tab != 'delete'"
          >批量删除</el-button
        >
        <el-button
          type="warning"
          size="small"
          @click="handleRestoreGoods()"
          v-else
          >恢复商品</el-button
        >

        <el-popconfirm
          v-if="searchForm.tab == 'delete'"
          title="是否彻底删除该商品?"
          confirm-button-text="确认"
          cancel-button-text="取消"
          @confirm.stop="handleDestoryGoods()"
        >
          <template #reference>
            <el-button type="danger" size="small">彻底删除</el-button>
          </template>
        </el-popconfirm>

        <el-button
          size="small"
          @click="handleMultiStatusChange(1)"
          v-if="searchForm.tab == 'all' || searchForm.tab == 'off'"
          >上架</el-button
        >
        <el-button
          size="small"
          @click="handleMultiStatusChange(0)"
          v-if="searchForm.tab == 'all' || searchForm.tab == 'saling'"
          >下架</el-button
        >
      </ListHeader>

      <el-table
        :data="tableData"
        stripe
        style="width: 100%"
        v-loading="loading"
        ref="multipleTableRef"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="商品" width="300">
          <template #default="{ row }">
            <div class="flex items-center">
              <el-image
                class="mr-3 rounded"
                :src="row.cover"
                fit="cover"
                :lazy="true"
                style="width: 50px; height: 50px"
              ></el-image>
              <div class="flex-1">
                <p>{{ row.title }}</p>
                <div>
                  <span class="text-rose-500">{{ row.min_price }}</span>
                  <el-divider direction="vertical" />
                  <span class="text-gray-500 text-xs">{{ row.min_price }}</span>
                </div>
                <p class="text-gray-400 text-xs mb-1">
                  分类{{ row.category ? row.category.name : "未分类" }}
                </p>
                <p class="text-gray-400 text-xs">
                  创建时间:{{ row.create_time }}
                </p>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="sale_count"
          label="实际销量"
          width="100"
          align="center"
        />
        <el-table-column label="商品状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status ? 'success' : 'danger'" size="small">{{
              row.status ? "上架" : "仓库"
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="审核状态"
          width="120"
          align="center"
          v-if="searchForm.tab != 'delete'"
        >
          <template #default="{ row }">
            <div class="flex flex-col" v-if="row.ischeck == 0">
              <el-button type="success" size="small" plain>审核通过</el-button>
              <el-button type="danger" size="small" plain class="mt-2 !ml-0"
                >审核拒绝</el-button
              >
            </div>
            <span v-else>{{ row.ischeck == 1 ? "通过" : "拒绝" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="stock"
          label="总库存"
          width="90"
          align="center"
        />
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <div v-if="searchForm.tab != 'delete'">
              <el-button
                class="px-1"
                type="primary"
                size="small"
                text
                @click="handleEdit(scope.row)"
                >修改</el-button
              >
              <el-button
                class="px-1"
                :type="
                  (scope.row.sku_type == 0 && !scope.row.skus_value) ||
                  (scope.row.sku_type == 1 && scope.row.goods_skus.length == 0)
                    ? 'danger'
                    : 'primary'
                "
                size="small"
                text
                @click="handleSetGoodsSkus(scope.row)"
                :loading="scope.row.skusLoading"
                >商品规格</el-button
              >
              <el-button
                class="px-1"
                :type="
                  scope.row.goods_banner.length == 0 ? 'danger' : 'primary'
                "
                size="small"
                text
                @click="handleSetGoodsBanner(scope.row)"
                :loading="scope.row.bannersLoading"
                >设置轮播图</el-button
              >
              <el-button
                class="px-1"
                :type="scope.row.content == 0 ? 'danger' : 'primary'"
                size="small"
                text
                @click="handleSetGoodsContent(scope.row)"
                :loading="scope.row.contentLoading"
                >商品详情</el-button
              >
              <el-popconfirm
                title="是否要删除该商品?"
                confirm-button-text="确认"
                cancel-button-text="取消"
                @confirm.stop="handleDelete(scope.row.id)"
              >
                <template #reference>
                  <el-button text class="px-1" type="primary" size="small"
                    >删除</el-button
                  >
                </template>
              </el-popconfirm>
            </div>
            <span v-else>暂无操作</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="flex justify-center items-center mt-5">
        <el-pagination
          background
          layout="prev,pager,next"
          :total="total"
          v-model:current-page="currentPage"
          :page-size="limit"
          @current-change="getData"
        />
      </div>

      <!-- 抽屉组件 -->
      <FormDrawer
        ref="formDrawerRef"
        :title="drawerTitle"
        @submit="handleSubmit"
      >
        <el-form
          :model="form"
          ref="formRef"
          :rules="rules"
          label-width="80px"
          :inline="false"
        >
          <el-form-item label="商品名称" prop="title">
            <el-input
              v-model="form.title"
              placeholder="请输入商品名称,不能超过60个字符"
            ></el-input>
          </el-form-item>
          <el-form-item label="封面" prop="cover">
            <ChooseImage v-model="form.cover" />
          </el-form-item>

          <el-form-item label="商品分类" prop="category_id">
            <el-select
              v-model="form.category_id"
              placeholder="选择所属商品分类"
            >
              <el-option
                v-for="item in category_list"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="商品描述" prop="desc">
            <el-input
              v-model="form.desc"
              placeholder="选填,商品卖点"
              type="textarea"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位" prop="unit">
            <el-input
              v-model="form.unit"
              placeholder="单位"
              style="width: 50%"
            ></el-input>
          </el-form-item>

          <el-form-item label="总库存" prop="stock">
            <el-input
              v-model="form.stock"
              placeholder="总库存"
              type="number"
              style="width: 40%"
            >
              <template #append> 件 </template>
            </el-input>
          </el-form-item>
          <el-form-item label="库存预警" prop="min_stock">
            <el-input
              v-model="form.min_stock"
              placeholder="库存预警"
              type="number"
              style="width: 40%"
            >
              <template #append> 件 </template>
            </el-input>
          </el-form-item>
          <el-form-item label="最低销售价" prop="min_price">
            <el-input
              v-model="form.min_price"
              placeholder="最低销售价"
              type="number"
              style="width: 40%"
            >
              <template #append> 元 </template>
            </el-input>
          </el-form-item>
          <el-form-item label="最低原价" prop="min_oprice">
            <el-input
              v-model="form.min_oprice"
              placeholder="最低原价"
              type="number"
              style="width: 40%"
            >
              <template #append> 件 </template>
            </el-input>
          </el-form-item>
          <el-form-item label="库存显示" prop="stock_display">
            <el-radio-group v-model="form.stock_display">
              <el-radio :label="0">隐藏</el-radio>
              <el-radio :label="1">显示</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否上架" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="0">放入仓库</el-radio>
              <el-radio :label="1">立即上架</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </FormDrawer>
    </el-card>

    <banners ref="bannersRef" @reload-data="getData" />
    <content ref="contentRef" @reload-data="getData" />
    <skus ref="skusRef" @reload-data="getData" />
  </div>
</template>

<script setup>
import { ref } from "vue";
import ListHeader from "@/components/ListHeader.vue";
import FormDrawer from "@/components/FormDrawer.vue";
import ChooseImage from "@/components/ChooseImage.vue";
import Search from "@/components/Search.vue";
import SearchItem from "@/components/SearchItem.vue";
import banners from "./banners.vue";
import content from "./content.vue";
import skus from "./skus.vue";
import { Eptoast } from "@/utils/util";

import {
  getGoodsList,
  updateGoodsStatus,
  createGoods,
  updateGoods,
  deleteGoods,
  restoreGoods,
  destroyGoods,
} from "@/api/goods.js";

import { getCategoryList } from "@/api/category";
import { useInitTable, useInitForm } from "@/utils/useCommon.js";

const {
  handleSelectionChange,
  handleMultiDelete,
  multipleTableRef,
  searchForm,
  resetSearchForm,
  tableData,
  loading,
  currentPage,
  total,
  limit,
  getData,
  handleDelete,
  handleMultiStatusChange,
  multiSelectionIds,
} = useInitTable({
  searchForm: {
    title: "",
    tab: "all",
    category_id: null,
  },
  getList: getGoodsList,
  onGetListSuccess: (res) => {
    tableData.value = res.data.list.map((o) => {
      o.bannersLoading = false;
      o.contentLoading = false;
      o.skusLoading = false;
      return o;
    });
    total.value = res.data.totalCount;
  },
  delete: deleteGoods,
  updateStatus: updateGoodsStatus,
});

const {
  formDrawerRef,
  formRef,
  form,
  rules,
  editId,
  drawerTitle,
  handleSubmit,
  resetForm,
  handleCreate,
  handleEdit,
} = useInitForm({
  form: {
    title: null, //商品名称
    category_id: null, //商品分类
    cover: null, //商品封面
    desc: null, //商品描述
    unit: "件", //商品单位
    stock: 100, //库存
    min_stock: 10, //最小库存
    status: 1, //状态
    stock_display: 1, // 0是隐藏,1是显示库存显示
    min_price: 0, //最低销售价
    min_oproce: 0, //最低原价
  },
  getData,
  update: updateGoods,
  create: createGoods,
});

// all 全部 checking审核中 saling出售中  off下架 min_stock库存预警 delete回收站
const tabbars = [
  {
    key: "all",
    name: "全部",
  },
  {
    key: "checking",
    name: "审核",
  },
  {
    key: "saling",
    name: "出售中",
  },
  {
    key: "off",
    name: "下架",
  },
  {
    key: "min_stock",
    name: "库存预警",
  },
  {
    key: "delete",
    name: "回收站",
  },
];

// 高级搜索框部分

//商品分类
const category_list = ref([]);
getCategoryList().then((res) => {
  category_list.value = res.data;
});

//设置轮播图相关
const bannersRef = ref(null);
const handleSetGoodsBanner = (row) => {
  bannersRef.value.open(row);
};

// 设置商品详情相关
const contentRef = ref(null);
const handleSetGoodsContent = (row) => {
  contentRef.value.open(row);
};

// 设置商品规格相关
const skusRef = ref(null);
const handleSetGoodsSkus = (row) => {
  skusRef.value.open(row);
};

const handleRestoreGoods = () => {
  useMultiAction(restoreGoods, "恢复");
  // loading.value = true;
  // restoreGoods(multiSelectionIds.value)
  //   .then((res) => {
  //     Eptoast("恢复成功");
  //     if (multipleTableRef.value) {
  //       multipleTableRef.value.clearSelection();
  //     }
  //     getData();
  //   })
  //   .finally(() => {
  //     loading.value = false;
  //   });
};

const handleDestoryGoods = () => {
  useMultiAction(destroyGoods, "彻底删除");
};
// loading.value = true;
// destroyGoods(multiSelectionIds.value)
//   .then((res) => {
//     Eptoast("彻底删除成功");
//     if (multipleTableRef.value) {
//       multipleTableRef.value.clearSelection();
//     }
//     getData();
//   })
//   .finally(() => {
//     loading.value = false;
//   });
// };

function useMultiAction(func, msg) {
  console.log("multiSelectionIds.value");
  console.log(multiSelectionIds.value);
  loading.value = true;
  func(multiSelectionIds.value)
    .then((res) => {
      Eptoast(msg + "成功");
      if (multipleTableRef.value) {
        multipleTableRef.value.clearSelection();
      }
      getData();
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>
