<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-08-21 14:48:58
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-22 11:48:13
 * @FilePath: /admin_vue3_element_plus/src/views/goods/components/SkuTable.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-form-item label="规格设置">
    <table class="border">
      <thead>
        <tr>
          <th
            class="border"
            v-for="(th, thi) in tableThs"
            :key="thi"
            :width="th.width"
            :rowspan="th.rowspan"
            :colspan="th.colspan"
          >
            {{ th.name }}
          </th>
        </tr>
        <tr>
          <th class="border" v-for="(th, thi) in skuLabels" :key="thi">
            {{ th.name }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in sku_list" :key="index">
          <td
            width="100"
            class="border text-center"
            v-for="(sku, skuI) in item"
            :key="skuI"
          >
            {{ sku.value }}
          </td>
          <td class="border">
            <el-input
              v-model="item.pprice"
              size="small"
              type="number"
            ></el-input>
          </td>
          <td class="border">
            <el-input
              v-model="item.oprice"
              size="small"
              type="number"
            ></el-input>
          </td>
          <td class="border">
            <el-input
              v-model="item.cprice"
              size="small"
              type="number"
            ></el-input>
          </td>
          <td class="border">
            <el-input
              v-model="item.stock"
              size="small"
              type="number"
            ></el-input>
          </td>
          <td class="border">
            <el-input
              v-model="item.volume"
              size="small"
              type="number"
            ></el-input>
          </td>
          <td class="border">
            <el-input
              v-model="item.weight"
              size="small"
              type="number"
            ></el-input>
          </td>
          <td class="border">
            <el-input v-model="item.code" size="small"></el-input>
          </td>
        </tr>
      </tbody>
    </table>
  </el-form-item>
</template>

<script setup>
import { initSkuTable } from "@/utils/useSku";
const { skuLabels, tableThs, sku_list } = initSkuTable();
</script>

<style scoped lang="scss"></style>
