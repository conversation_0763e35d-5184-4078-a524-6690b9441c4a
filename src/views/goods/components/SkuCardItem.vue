<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-08-12 16:04:53
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-20 11:38:11
 * @FilePath: /admin_vue3_element_plus/src/views/goods/components/SkuCardItem.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- <div v-for="(tag, index) in item.goodsSkusCardValue" :key="index">
    {{ tag.text }}
  </div> -->
  <div class="flex gap-2" v-loading="loading">
    <el-tag
      v-for="(tag, index) in item.goodsSkusCardValue"
      :key="index"
      closable
      :disable-transitions="false"
      @close="handleClose(tag)"
      effect="plain"
    >
      <el-input
        class="w-20 ml-[-10px]"
        v-model="tag.text"
        placeholder="选项值"
        size="small"
        @change="handleChange($event, tag)"
      ></el-input>
    </el-tag>
    <el-input
      v-if="inputVisible"
      ref="InputRef"
      v-model="inputValue"
      class="w-20"
      size="small"
      @keyup.enter="handleInputConfirm"
      @blur="handleInputConfirm"
    />
    <el-button v-else class="button-new-tag" size="small" @click="showInput">
      增加
    </el-button>
  </div>
</template>

<script setup>
import { initSkusCardItem } from "@/utils/useSku";

const props = defineProps({
  skuCardId: [Number, String],
});
const {
  item,
  inputValue,
  inputVisible,
  InputRef,
  handleClose,
  showInput,
  handleInputConfirm,
  loading,
  handleChange,
} = initSkusCardItem(props.skuCardId);
</script>

<style scoped lang="scss"></style>
