<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-08-12 15:40:41
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-22 15:08:51
 * @FilePath: /admin_vue3_element_plus/src/views/goods/components/SkuCard.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-form-item label="规格选项" v-loading="bodyLoading">
    <el-card
      shadow="never"
      class="w-full mb-3"
      v-for="(item, index) in sku_card_list"
      :key="index"
      v-loading="item?.loading"
    >
      <!-- {{ item }} -->
      <template #header>
        <div class="flex items-center">
          <el-input
            v-model="item.text"
            placeholder="规格选项"
            style="width: 300px"
            @change="updateSkuCardEvent(item)"
          >
            <template #append>
              <el-icon class="cursor-pointer" @click="handleChooseSku(item)"
                ><more
              /></el-icon>
            </template>
          </el-input>
          <el-button
            size="small"
            class="ml-auto"
            @click="sortCard('up', index)"
            :disabled="index === 0"
            ><el-icon><Top /></el-icon
          ></el-button>
          <el-button
            size="small"
            @click="sortCard('down', index)"
            :disabled="index === sku_card_list.length - 1"
            ><el-icon><Bottom /></el-icon
          ></el-button>

          <el-popconfirm
            title="是否要删除该选项?"
            confirm-button-text="确认"
            cancel-button-text="取消"
            @confirm.stop="handleDelete(item)"
          >
            <template #reference>
              <el-button size="small"
                ><el-icon><Delete /></el-icon
              ></el-button>
            </template>
          </el-popconfirm>
        </div>
      </template>
      <!-- card body -->
      <SkuCardItem :skuCardId="item.id" />
    </el-card>
    <el-button
      type="success"
      size="small"
      :loading="btnLoading"
      @click="addSkuCardEvent"
      >添加规格选项</el-button
    >
  </el-form-item>
  <ChooseSku ref="chooseSkuRef" />
</template>

<script setup>
import { ref } from "vue";
import { Delete } from "@element-plus/icons-vue";
import SkuCardItem from "./SkuCardItem.vue";
import ChooseSku from "@/components/ChooseSku.vue";
import {
  sku_card_list,
  addSkuCardEvent,
  btnLoading,
  handleDelete,
  updateSkuCardEvent,
  sortCard,
  bodyLoading,
  handleChooseSetGoodsSkusCard,
} from "@/utils/useSku";

const chooseSkuRef = ref(null);
const handleChooseSku = (item) => {
  // open() 中传递的是一个函数，这里需要执行函数
  chooseSkuRef.value.open((value) => {
    handleChooseSetGoodsSkusCard(item.id, {
      name: value.name,
      value: value.list,
    });
  });
};
</script>

<style>
.el-card {
  @apply !p-2 bg-gray-50;
}
</style>
