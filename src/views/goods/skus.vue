<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-08-01 15:40:15
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-22 15:07:54
 * @FilePath: /admin_vue3_element_plus/src/views/goods/banners.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <FormDrawer
    ref="formDrawerRef"
    title="设置商品规格"
    @submit="submit"
    destroy-on-close
    size="70%"
  >
    <el-form :model="state" ref="form" :inline="false">
      <el-form-item label="规格类型">
        <el-radio-group v-model="state.sku_type" @change="">
          <el-radio :label="0"> 单规格 </el-radio>
          <el-radio :label="1"> 多规格 </el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="state.sku_type == 0">
        <el-form-item label="市场价格">
          <el-input
            v-model="state.sku_value.oprice"
            style="width: 35%"
            clearable
            @change=""
          >
            <template #append>元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="销售价格">
          <el-input
            v-model="state.sku_value.pprice"
            style="width: 35%"
            clearable
            @change=""
          >
            <template #append>元</template></el-input
          >
        </el-form-item>
        <el-form-item label="成本价格">
          <el-input
            v-model="state.sku_value.cprice"
            style="width: 35%"
            clearable
            @change=""
          >
            <template #append>元</template></el-input
          >
        </el-form-item>
        <el-form-item label="商品重量 ">
          <el-input
            v-model="state.sku_value.weight"
            style="width: 35%"
            clearable
            @change=""
          >
            <template #append>公斤</template></el-input
          >
        </el-form-item>
        <el-form-item label="商品体积">
          <el-input
            v-model="state.sku_value.volume"
            style="width: 35%"
            clearable
            @change=""
          >
            <template #append>立方米</template></el-input
          >
        </el-form-item>
      </template>
      <template v-else>
        <SkuCard />
        <SkuTable />
      </template>
    </el-form>
  </FormDrawer>
</template>

<script setup>
import { ref, reactive } from "vue";
import FormDrawer from "@/components/FormDrawer.vue";
import { readGoods, updateGoodsSkus } from "@/api/goods.js";
import { Eptoast } from "@/utils/util";
import SkuCard from "./components/SkuCard.vue";
import SkuTable from "./components/SkuTable.vue";
import { goodsId, initSkuCardList, sku_list } from "@/utils/useSku";

const formDrawerRef = ref(null);
const state = reactive({
  sku_type: 0,
  sku_value: { oprice: 0, pprice: 0, cprice: 0, weight: 0, volume: 0 },
});

// 打开这个抽屉
const open = (row) => {
  goodsId.value = row.id;
  row.skusLoading = true;
  readGoods(goodsId.value)
    .then((res) => {
      state.sku_type = res.data.sku_type;
      state.sku_value = res.data.sku_value || {
        oprice: 0,
        pprice: 0,
        cprice: 0,
        weight: 0,
        volume: 0,
      };
      initSkuCardList(res.data);
      formDrawerRef.value.open();
    })
    .finally(() => {
      row.skusLoading = false;
    });
};
const emit = defineEmits(["reloadData"]);
const submit = () => {
  formDrawerRef.value.showLoading();
  let data = {
    sku_type: state.sku_type,
    sku_value: state.sku_value,
  };
  if (state.sku_type == 1) {
    data.goodsSkus = sku_list.value;
  }
  updateGoodsSkus(goodsId.value, data)
    .then((res) => {
      Eptoast("设置商品规格成功");
      formDrawerRef.value.close();
      emit("reloadData");
    })
    .finally(() => {
      formDrawerRef.value.hideLoading();
    });
};

defineExpose({
  open,
});
</script>

<style scoped lang="scss"></style>
