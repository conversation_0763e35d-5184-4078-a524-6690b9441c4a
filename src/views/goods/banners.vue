<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-08-01 15:40:15
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-05 15:07:14
 * @FilePath: /admin_vue3_element_plus/src/views/goods/banners.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-drawer
    title="设置商品详情"
    v-model="dialogVisible"
    size="50%"
    destroy-on-close
  >
    <el-form :model="state" ref="form" label-width="80px" :inline="false">
      <el-form-item label="轮播图">
        <ChooseImage v-model="state.banners" :limit="9" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit" :loading="loading"
          >提交</el-button
        >
      </el-form-item>
    </el-form>
  </el-drawer>
</template>

<script setup>
import { ref, reactive } from "vue";
import ChooseImage from "@/components/ChooseImage.vue";
import { readGoods, setGoodsBanner } from "@/api/goods.js";
import { Eptoast } from "@/utils/util";
const dialogVisible = ref(false);
const state = reactive({ banners: [] });

// 打开这个抽屉
const goodsId = ref(0);
const open = (row) => {
  goodsId.value = row.id;
  row.bannersLoaded = true;
  readGoods(goodsId.value)
    .then((res) => {
      console.log(res);
      state.banners = res.data.goodsBanner.map((o) => o.url);
      dialogVisible.value = true;
    })
    .finally(() => {
      row.bannersLoaded = false;
    });
};
const emit = defineEmits(["reloadData"]);
const loading = ref(false);
const submit = () => {
  loading.value = true;
  setGoodsBanner(goodsId.value, state)
    .then((res) => {
      Eptoast("设置轮播图成功");
      dialogVisible.value = false;
      emit("reloadData");
    })
    .finally(() => {
      loading.value = false;
    });
};

defineExpose({
  open,
});
</script>

<style scoped lang="scss"></style>
