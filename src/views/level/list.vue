<!--
 * @Author: yangy <EMAIL>
 * @Date: 2023-12-25 16:35:42
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-23 12:29:12
 * @FilePath: /admin_vue3_element_plus/src/views/notice/list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-card shadow="never" class="border-0">
    <!-- 新增|刷新 -->
    <ListHeader @create="handleCreate" @refresh="getData" />
    <!-- {{ tableData }} -->
    <el-table :data="tableData" stripe style="width: 100%" v-loading="loading">
      <el-table-column prop="name" label="会员等级" />
      <el-table-column prop="discount" label="折扣率" align="center" />
      <el-table-column prop="level" label="等级序号" align="center" />
      <el-table-column label="状态" width="120">
        <template #default="{ row }">
          <el-switch
            :modelValue="row.status"
            :active-value="1"
            :inactive-value="0"
            :disabled="row.super == 1"
            :loading="row.statusLoading"
            @change="handleStatusChange($event, row)"
          >
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            text
            @click="handleEdit(scope.row)"
            >修改</el-button
          >
          <el-popconfirm
            title="是否要删除该记录?"
            confirm-button-text="确认"
            cancel-button-text="取消"
            @confirm.stop="handleDelete(scope.row.id)"
          >
            <template #reference>
              <el-button class="px-1" type="primary" size="small" text
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="flex justify-center items-center mt-5">
      <el-pagination
        background
        layout="prev,pager,next"
        :total="total"
        v-model:current-page="currentPage"
        :page-size="limit"
        @current-change="getData"
      />
    </div>

    <!-- 抽屉组件 -->
    <!-- 增加和编辑 -->
    <FormDrawer ref="formDrawerRef" :title="drawerTitle" @submit="handleSubmit">
      <el-form
        :model="form"
        ref="formRef"
        :rules="rules"
        label-width="80px"
        :inline="false"
      >
        <el-form-item label="等级名称" prop="name">
          <el-input v-model="form.name" placeholder="等级名称"></el-input>
        </el-form-item>
        <el-form-item label="等级权重" prop="level">
          <el-input
            v-model="form.level"
            placeholder="等级权重"
            type="number"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="form.status"
            :active-value="1"
            :inactive-value="0"
          >
          </el-switch>
        </el-form-item>
        <el-form-item label="升级条件">
          <!-- <el-input v-model="form.max_price" placeholder="" style="width: 50%">
            <template #prepend>累加消费</template>
            <template #append>元</template>
          </el-input>
          <span class="text-xs text-gray-400 my-2"
            >设置会员等级所需要的累计消费必须大于等于0,单位：元</span
          >
          <el-input v-model="form.max_times" placeholder="" style="width: 50%">
            <template #prepend>累计次数满</template>
            <template #append>次</template>
          </el-input>
          <span class="text-xs text-light-100 text-gray-400 my-2"
            >设置会员等级所需要的累计消费必须大于等于0,单位：元</span
          > -->
          <div>
            <small class="text-xs mr-2">累计消费满</small>
            <el-input
              v-model="form.max_price"
              placeholder="累计消费"
              type="number"
              style="width: 50%"
            >
              <template #append> 元 </template>
            </el-input>
            <small class="text-gray-400 flex"
              >设置会员等级所需要的累计消费必须大于等于0,单位:元</small
            >
            <small class="text-xs mr-2">累计次数满</small>
            <el-input
              v-model="form.max_times"
              placeholder="累计次数"
              type="number"
              style="width: 50%"
            >
              <template #append> 次 </template>
            </el-input>
            <small class="text-gray-400 flex"
              >设置会员等级所需要的累计消费必须大于等于0,单位:元</small
            >
          </div>
        </el-form-item>
        <el-form-item label="折扣率(%)" prop="discount">
          <el-input
            v-model="form.discount"
            placeholder="折扣率(%)"
            type="number"
            style="width: 50%"
          >
            <template #append> % </template>
          </el-input>
          <small class="text-gray-400 flex"
            >折扣率单位为百分比,如输入90,表示该会员等级的用户可以以商品原价的90%的价格购买</small
          >
        </el-form-item>
      </el-form>
    </FormDrawer>
  </el-card>
</template>

<script setup>
import FormDrawer from "@/components/FormDrawer.vue";
import ListHeader from "@/components/ListHeader.vue";
import {
  getUserLevelList,
  createUserLevel,
  updateUserLevel,
  deleteUserLevel,
  updateUserLevelStatus,
} from "@/api/level.js";
import { useInitTable, useInitForm } from "@/utils/useCommon.js";
const {
  tableData,
  loading,
  currentPage,
  total,
  limit,
  getData,
  handleDelete,
  handleStatusChange,
} = useInitTable({
  getList: getUserLevelList,
  delete: deleteUserLevel,
  updateStatus: updateUserLevelStatus,
});

const {
  formDrawerRef,
  formRef,
  form,
  rules,
  editId,
  drawerTitle,
  handleSubmit,
  resetForm,
  handleCreate,
  handleEdit,
} = useInitForm({
  form: {
    name: "",
    level: 100,
    status: 1,
    discount: 0,
    max_price: 0,
    max_time: 0,
  },
  rules: {
    name: [
      {
        required: true,
        message: "会员等级名称不能为空",
        trigger: "blur",
      },
    ],
  },
  getData,
  update: updateUserLevel,
  create: createUserLevel,
});
</script>
