<!--
 * @Author: yangy
 * @Date: 2023-12-12 15:06:58
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-23 15:00:23
 * @FilePath: /admin_vue3_element_plus/src/views/login.vue
 * @Description: 
 * 
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved. 
-->
<template>
  <el-row class="login-container">
    <el-col :lg="16" :md="12" class="left">
      <div>
        <div class="font-bold text-5xl text-light-50 mb-4">青柠商城</div>
        <div class="text-gray-200 text-sm">青柠利合 qn_mall 青柠商城</div>
      </div>
    </el-col>
    <el-col
      :lg="8"
      :md="12"
      class="bg-light-50 flex items-center justify-center flex-col"
    >
      <h2 class="font-bold text-3xl text-gray-800">欢迎回来</h2>
      <div
        class="flex items-center justify-center my-5 text-gray-300 space-x-2"
      >
        <span class="h-[1px] w-16 bg-gray-200"></span>
        <span>账号密码登录</span>
        <span class="h-[1px] w-16 bg-gray-200"></span>
      </div>
      <el-form ref="formRef" :rules="rules" :model="form" class="w-[250px]">
        <el-form-item prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名">
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="form.password"
            type="password"
            show-password
            placeholder="请输入密码"
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            round
            color="#626aef"
            class="w-[250px]"
            @click="onSubmit"
            :loading="loading"
            >登 录</el-button
          >
        </el-form-item>
      </el-form>
    </el-col>
  </el-row>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
// import {login} from '@/api/manager'
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import { Eptoast } from "@/utils/util";
// import { setToken } from '@/utils/auth'

const router = useRouter();
const formRef = ref(null);
const loading = ref(false);
const store = useStore();
// do not use same name with ref
const form = reactive({
  username: "",
  password: "",
});
// 表单验证
const rules = {
  username: [
    {
      required: true, // 是否必须
      message: "用户名不能为空", //提示信息
      trigger: "blur", // 触发时机
    },
    {
      min: 3,
      max: 15,
      message: "长度应该在3-15个字符之间",
      trigger: "blur",
    },
  ],
  password: [
    {
      required: true,
      message: "密码不能为空",
      trigger: "blur",
    },
  ],
};

// 登录
const onSubmit = () => {
  // 点击登录的时候触发验证
  formRef.value.validate((valid) => {
    if (!valid) {
      return false;
    }
  });
  loading.value = true; // 加载中

  store
    .dispatch("login", form)
    .then((res) => {
      Eptoast("登录成功");
      router.push("/");
    })
    .finally(() => {
      loading.value = false; // 加载完毕 取消转圈圈
    });
};
//监听回车事件
function onKeyUp(e) {
  console.log(e.key);
  if (e.key == "Enter") {
    onSubmit();
  }
}

onMounted(() => {
  document.addEventListener("keyup", onKeyUp);
});
</script>

<style lang="scss" scoped>
.login-container {
  @apply min-h-screen bg-indigo-500;
}
.login-container .left {
  @apply flex items-center justify-center;
}
</style>
