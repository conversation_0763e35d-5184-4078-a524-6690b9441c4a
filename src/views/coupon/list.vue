<!--
 * @Author: yangy <EMAIL>
 * @Date: 2023-12-25 16:35:42
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-04-24 17:31:23
 * @FilePath: /admin_vue3_element_plus/src/views/coupon/list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-card shadow="never" class="border-0">
    <!-- 新增|刷新 -->
    <ListHeader @create="handleCreate" @refresh="getData" />
    <el-table :data="tableData" stripe style="width: 100%" v-loading="loading">
      <el-table-column prop="title" label="优惠券名称" width="350">
        <template #default="{ row }">
          <div
            class="border border-dashed py-2 px-4 rounded"
            :class="row.statusText == '领取中' ? 'active' : 'inactive'"
          >
            <h5>{{ row.name }}</h5>
            <small>{{ row.start_time }} ~ {{ row.end_time }}</small>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="statusText" label="状态" />
      <el-table-column label="优惠">
        <template #default="{ row }">
          {{ row.type ? "满减" : "折扣" }}
          {{ row.type ? "$" + row.value : row.value + "折" }}
        </template>
      </el-table-column>
      <el-table-column prop="total" label="发放数量" />
      <el-table-column prop="used" label="已使用" />
      <el-table-column label="操作" width="160" align="center">
        <template #default="scope">
          <el-button
            v-if="scope.row.statusText == '未开始'"
            type="primary"
            size="small"
            text
            @click="handleEdit(scope.row)"
            >修改</el-button
          >
          <el-popconfirm
            v-if="scope.row.statusText != '领取中'"
            title="是否要删除该优惠券?"
            confirm-button-text="确认"
            cancel-button-text="取消"
            @confirm.stop="handleDelete(scope.row.id)"
          >
            <template #reference>
              <el-button class="px-1" type="primary" size="small" text
                >删除</el-button
              >
            </template>
          </el-popconfirm>
          <el-popconfirm
            v-if="scope.row.statusText == '领取中'"
            title="是否要让该优惠券失效?"
            confirmButtonText="失效"
            cancelButtonText="取消"
            @confirm.stop="handleStatusChange(0, scope.row)"
          >
            <template #reference>
              <el-button class="px-1" type="danger" size="small" text
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="flex justify-center items-center mt-5">
      <el-pagination
        background
        layout="prev,pager,next"
        :total="total"
        v-model:current-page="currentPage"
        :page-size="limit"
        @current-change="getData"
      />
    </div>

    <!-- 抽屉组件 -->
    <FormDrawer ref="formDrawerRef" :title="drawerTitle" @submit="handleSubmit">
      <el-form
        :model="form"
        ref="formRef"
        :rules="rules"
        label-width="80px"
        :inline="false"
      >
        <el-form-item label="优惠券名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="优惠券名称"
            style="50%"
          ></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio :label="0">满减</el-radio>
            <el-radio :label="1">折扣</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="面值" prop="value">
          <el-input v-model="form.value" placeholder="面值" style="width: 50%">
            <template #append> {{ form.type ? "折" : "元" }} </template>
          </el-input>
        </el-form-item>
        <el-form-item label="发行量" prop="total">
          <el-input-number
            v-model="form.total"
            :min="0"
            :max="10000"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="最低使用价格" prop="min_price">
          <el-input
            v-model="form.min_price"
            placeholder="最低使用价格"
            type="number"
          ></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="order">
          <el-input-number
            v-model="form.order"
            placeholder="排序"
            :min="0"
            :max="1000"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="活动时间">
          <el-date-picker
            :editable="false"
            v-model="timerange"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="To"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="描述" prop="desc">
          <el-input
            v-model="form.desc"
            placeholder="描述"
            type="textarea"
            :row="5"
          ></el-input>
        </el-form-item>
      </el-form>
    </FormDrawer>
  </el-card>
</template>

<script setup>
import { computed } from "vue";
import FormDrawer from "@/components/FormDrawer.vue";
import ListHeader from "@/components/ListHeader.vue";
import {
  getCouponList,
  createCoupon,
  updateCoupon,
  deleteCoupon,
  updateCouponStatus,
} from "@/api/coupon.js";
import { useInitTable, useInitForm } from "@/utils/useCommon.js";

function formatStatus(row) {
  let s = "领取中";
  let start_time = new Date(row.start_time).getTime();
  let now = new Date().getTime();
  let end_time = new Date(row.end_time).getTime();
  if (start_time > now) {
    s = "未开始";
  } else if (end_time < now) {
    s = "已结束";
  } else if (row.status == 0) {
    s = "已失效";
  }
  return s;
}
const {
  tableData,
  loading,
  currentPage,
  total,
  limit,
  getData,
  handleDelete,
  handleStatusChange,
} = useInitTable({
  getList: getCouponList,
  onGetListSuccess: ({ data }) => {
    tableData.value = data.list.map((o) => {
      //转化优惠券状态
      o.statusText = formatStatus(o);
      return o;
    });
    total.value = data.totalCount;
  },
  delete: deleteCoupon,
  updateStatus: updateCouponStatus,
});

const {
  formDrawerRef,
  formRef,
  form,
  rules,
  editId,
  drawerTitle,
  handleSubmit,
  resetForm,
  handleCreate,
  handleEdit,
} = useInitForm({
  form: {
    name: "",
    type: 0,
    value: 0,
    total: 100,
    min_price: 0,
    start_time: null,
    end_time: null,
    order: 50,
    desc: "",
  },
  rules: {
    name: [
      {
        required: true,
        message: "优惠券名称不能为空",
        trigger: "blur",
      },
    ],
  },
  getData,
  update: updateCoupon,
  create: createCoupon,

  beforeSubmit: (f) => {
    if (typeof f.start_time != "number") {
      f.start_time = new Date(f.start_time).getTime();
    }

    if (typeof f.end_time != "number") {
      f.end_time = new Date(f.end_time).getTime();
    }

    return f;
  },
});

const timerange = computed({
  get() {
    return form.start_time && form.end_time
      ? [form.start_time, form.end_time]
      : [];
  },
  set(value) {
    form.start_time = value[0];
    form.end_time = value[1];
  },
});
</script>

<style lang="scss" scoped>
.active {
  @apply border-rose-200 bg-rose-50 text-red-400;
}

.inactive {
  @apply border-gray-200 bg-gray-50 text-gray-400;
}
</style>
