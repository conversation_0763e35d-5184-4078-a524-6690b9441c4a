<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-28 15:31:47
 * @LastEditors: vinbrave <EMAIL>
 * @LastEditTime: 2024-02-29 21:44:49
 * @FilePath: /admin_vue3_element_plus/src/views/access/list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-card shadow="never" class="border-0">
      <ListHeader @create="handleCreate" @refresh="getData" />
      <!-- default-expanded-keys 默认展开一级菜单 -->
      <!-- @node-click="handleNodeClick" -->
      <el-tree
        :data="tableData"
        :props="defaultProps"
        v-loading="loading"
        node-key="id"
        :default-expanded-keys="defaultExpandedKeys"
      >
        <template #default="{ data }">
          <div class="custom-tree-node">
            <el-tag size="small" :type="data.menu ? '' : 'info'">{{
              data.menu ? "菜单" : "权限"
            }}</el-tag>
            <el-icon v-if="data.icon" :size="16" class="ml-2 mr-1">
              <component :is="data.icon" />
            </el-icon>
            <span>{{ data.name }}</span>

            <div class="ml-auto">
              <el-switch
                :modelValue="data.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange($event, data)"
              />
              <el-button
                type="primary"
                size="small"
                text
                @click.stop="handleEdit(data)"
                >修改</el-button
              >
              <el-button
                type="primary"
                size="small"
                text
                @click.stop="addChild(data.id)"
                >增加</el-button
              >
              <span @click.stop="() => {}">
                <el-popconfirm
                  title="是否要删除该记录?"
                  confirm-button-text="确认"
                  cancel-button-text="取消"
                  @confirm.stop="handleDelete(data.id)"
                >
                  <template #reference>
                    <el-button type="primary" size="small" text>删除</el-button>
                  </template>
                </el-popconfirm>
              </span>
            </div>
          </div>
        </template>
      </el-tree>

      <FormDrawer
        ref="formDrawerRef"
        :title="drawerTitle"
        @submit="handleSubmit"
      >
        <el-form
          :model="form"
          ref="formRef"
          :rules="rules"
          label-width="80px"
          :inline="false"
        >
          <!--emitPath: 在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值 -->
          <!-- checkStrictly 是否严格的遵守父子节点不互相关联 -->
          <!-- value  这个value 需要指定id 否则在切换选择的时候为空 -->
          <el-form-item label="上级菜单" prop="rule_id">
            <el-cascader
              v-model="form.rule_id"
              :options="options"
              :props="{
                value: 'id',
                label: 'name',
                children: 'child',
                checkStrictly: true,
                emitPath: false,
              }"
              placeholder="请选择上级菜单"
            />
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input
              v-model="form.name"
              style="width: 30%"
              placeholder="名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="菜单/规则" prop="menu">
            <el-radio-group v-model="form.menu">
              <el-radio :label="1" border>菜单</el-radio>
              <el-radio :label="0" border>规则</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- v-if="form.menu == 1"  根据某个条件来决定表单显示还是隐藏 -->
          <el-form-item label="菜单图标" prop="icon" v-if="form.menu == 1">
            <IconSelect v-model="form.icon" />
          </el-form-item>
          <el-form-item
            label="前端路由"
            prop="frontpath"
            v-if="form.menu == 1 && form.rule_id > 0"
          >
            <el-input
              v-model="form.frontpath"
              placeholder="前端路由"
            ></el-input>
          </el-form-item>
          <el-form-item label="后端规则" prop="condition" v-if="form.menu == 0">
            <el-input
              v-model="form.condition"
              placeholder="后端规则"
            ></el-input>
          </el-form-item>
          <el-form-item label="请求方式" prop="method" v-if="form.menu == 0">
            <el-select
              v-model="form.method"
              class="m-2"
              placeholder="选择请求方式"
              size="large"
              style="width: 240px"
            >
              <el-option
                v-for="item in ['GET', 'POST', 'PUT', 'DELETE']"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="排序" prop="order">
            <el-input-number v-model="form.order" :min="0" :max="1000" />
          </el-form-item>
        </el-form>
      </FormDrawer>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from "vue";
import ListHeader from "@/components/ListHeader.vue";
import FormDrawer from "@/components/FormDrawer.vue";
import IconSelect from "@/components/IconSelect.vue";
import {
  getRuleList,
  createRule,
  updateRule,
  updateRuleStatus,
  deleteRule,
} from "@/api/rule.js";
import { useInitTable, useInitForm } from "@/utils/useCommon.js";

const options = ref([]);
const defaultExpandedKeys = ref([]);
const { loading, tableData, getData, handleDelete, handleStatusChange } =
  useInitTable({
    getList: getRuleList,
    onGetListSuccess: (res) => {
      console.log(res.data);
      options.value = res.data.rules;
      tableData.value = res.data.list;
      defaultExpandedKeys.value = res.data.list.map((o) => o.id);
    },
    delete: deleteRule,
    updateStatus: updateRuleStatus,
  });

const {
  formDrawerRef,
  formRef,
  form,
  rules,
  editId,
  drawerTitle,
  handleSubmit,
  resetForm,
  handleCreate,
  handleEdit,
} = useInitForm({
  form: {
    rule_id: 0,
    menu: 0,
    name: "",
    condition: "",
    method: "GET",
    status: 1,
    order: 50,
    icon: "",
    frontpath: "",
  },
  rules: {},
  getData,
  update: updateRule,
  create: createRule,
});

// 添加子分类
const addChild = (id) => {
  // 先打开 再去赋值 否则handleCreate方法会重置表单
  handleCreate();
  form.rule_id = id;
  form.status = 1;
};

const defaultProps = {
  label: "name",
  children: "child",
};
</script>

<style lang="scss" scoped>
.custom-tree-node {
  display: flex;
  flex: 1;
  align-items: center;
  font-size: 14px;
  padding-right: 8px;
}
:deep(.el-tree-node__content) {
  padding: 15px 0;
}
</style>
