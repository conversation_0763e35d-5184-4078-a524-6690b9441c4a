<!--
 * @Author: yangy
 * @Date: 2023-12-20 12:26:13
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-02-27 10:15:28
 * @FilePath: /admin_vue3_element_plus/src/views/image/list.vue
 * @Description: 
 * 
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved. 
-->
<template>
  <el-container class="bg-white rounded" :style="{height :(h +'px')}">
    <el-header class="image-header">
      <el-button type="primary" size="small" @click="handleOpenCreate">新增图片分类</el-button>
      <el-button type="warning" size="small" @click="handleOpenUpload">上传图片</el-button>
    </el-header>
    <el-container>
      <!-- 侧边栏 -->
      <ImageAside ref="ImageAsideRef" @change="handleAsideChange"/>
      <!-- 主体部分 -->
      <ImageMain ref="ImageMainRef" />
    </el-container>
  </el-container>
</template>

<script setup>
  import {ref} from "vue"
  import ImageAside from "@/components/ImageAside.vue"
  import ImageMain from "@/components/ImageMain.vue"
  // 高度限定 总高度 减去 header 减去 主内容中的header 减去 padding
  const windowHeight = window.innerHeight || document.clientHeight || document.body.clientHeight
  const h = windowHeight - 64 - 44 -40
  //handelCreate
  const ImageAsideRef = ref(null)
  const ImageMainRef = ref(null)
  const handleOpenCreate = ()=>{
    ImageAsideRef.value.handelCreate()
  }
  const handleAsideChange= (image_class_id)=>ImageMainRef.value.loadData(image_class_id)

  const handleOpenUpload = ()=>ImageMainRef.value.openUploadFile()
</script>

<style>
.image-header {
  border-bottom: 1px solid #eee;
  @apply flex items-center;
}

</style>