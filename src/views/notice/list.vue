<!--
 * @Author: yangy <EMAIL>
 * @Date: 2023-12-25 16:35:42
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-02-29 12:04:26
 * @FilePath: /admin_vue3_element_plus/src/views/notice/list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-card shadow="never" class="border-0">
    <!-- 新增|刷新 -->
    <ListHeader @create="handleCreate" @refresh="getData" />
    <el-table :data="tableData" stripe style="width: 100%" v-loading="loading">
      <el-table-column prop="title" label="公告标题" />
      <el-table-column prop="content" label="内容" />
      <el-table-column prop="create_time" label="发布时间" />
      <el-table-column prop="update_time" label="更新时间" />
      <el-table-column prop="order" label="排序" />
      <el-table-column label="操作" width="160" align="center">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            text
            @click="handleEdit(scope.row)"
            >修改</el-button
          >
          <el-popconfirm
            title="是否要删除该公告?"
            confirm-button-text="确认"
            cancel-button-text="取消"
            @confirm.stop="handleDelete(scope.row.id)"
          >
            <template #reference>
              <el-button class="px-1" type="primary" size="small" text
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="flex justify-center items-center mt-5">
      <el-pagination
        background
        layout="prev,pager,next"
        :total="total"
        v-model:current-page="currentPage"
        :page-size="limit"
        @current-change="getData"
      />
    </div>

    <!-- 抽屉组件 -->
    <FormDrawer ref="formDrawerRef" :title="drawerTitle" @submit="handleSubmit">
      <el-form
        :model="form"
        ref="formRef"
        :rules="rules"
        label-width="80px"
        :inline="false"
      >
        <el-form-item label="公告标题" prop="title">
          <el-input v-model="form.title" placeholder="公告标题"></el-input>
        </el-form-item>
        <el-form-item label="公告内容" prop="content">
          <el-input
            v-model="form.content"
            placeholder="公告内容"
            type="textarea"
            rows="5"
          ></el-input>
        </el-form-item>
      </el-form>
    </FormDrawer>
  </el-card>
</template>

<script setup>
import FormDrawer from "@/components/FormDrawer.vue";
import ListHeader from "@/components/ListHeader.vue";
import {
  getNoticeList,
  createNotice,
  updateNotice,
  deleteNotice,
} from "@/api/notice.js";
import { useInitTable, useInitForm } from "@/utils/useCommon.js";

const { tableData, loading, currentPage, total, limit, getData, handleDelete } =
  useInitTable({
    getList: getNoticeList,
    delete: deleteNotice,
  });

const {
  formDrawerRef,
  formRef,
  form,
  rules,
  editId,
  drawerTitle,
  handleSubmit,
  resetForm,
  handleCreate,
  handleEdit,
} = useInitForm({
  form: {
    title: "",
    content: "",
  },
  rules: {
    title: [
      {
        required: true,
        message: "公告标题不能为空",
        trigger: "blur",
      },
    ],
    content: [
      {
        required: true,
        message: "公告内容不能为空",
        trigger: "blur",
      },
    ],
  },
  getData,
  update: updateNotice,
  create: createNotice,
});
</script>
