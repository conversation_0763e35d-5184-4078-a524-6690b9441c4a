<!--
 * @Author: yangy <EMAIL>
 * @Date: 2023-12-25 16:35:42
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-11-11 16:01:47
 * @FilePath: /admin_vue3_element_plus/src/views/notice/list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-card shadow="never" class="border-0">
    <Search :model="searchForm" @search="getData" @reset="resetSearchForm">
      <SearchItem label="关键词">
        <el-input
          v-model="searchForm.title"
          placeholder="商品标题"
          clearable
        ></el-input>
      </SearchItem>
    </Search>

    <el-table
      default-expand-all
      :data="tableData"
      stripe
      style="width: 100%"
      v-loading="loading"
    >
      <el-table-column type="expand">
        <template #default="{ row }">
          <div class="flex pl-18">
            <el-avatar
              shape="circle"
              :src="row.user.avatar"
              fit="fill"
              class="mr-3"
            ></el-avatar>
            <div class="flex-1">
              <h6 class="flex items-center">
                {{ row.user.nickname }} || {{ row.user.username }}
                <small class="text-gray-400 ml-2">{{ row.review_time }}</small>
                <el-button
                  class="ml-auto"
                  @click="openTextarea(row)"
                  v-if="!row.textareaEdit && !row.extra"
                  >回复</el-button
                >
              </h6>
              {{ row.review.data }}
              <div class="py-2">
                <el-image
                  v-for="(item, index) in row.review.image"
                  :key="index"
                  :src="item"
                  fit="cover"
                  style="width: 100px; height: 100px"
                  class="rounded"
                  :lazy="true"
                ></el-image>
              </div>
              <div v-if="row.textareaEdit">
                <el-input
                  v-model="textarea"
                  placeholder="请输入评价内容"
                  type="textarea"
                  :rows="2"
                ></el-input>
                <div class="py-2">
                  <el-button type="primary" @click="review(row)"
                    >回复</el-button
                  >
                  <el-button class="ml-2" @click="row.textareaEdit = false"
                    >取消</el-button
                  >
                </div>
              </div>

              <template v-else>
                <div
                  class="mt-3 bg-gray-100 p-3 rounded"
                  v-for="(item, index) in row.extra"
                  :key="index"
                >
                  <h6 class="flex font-bold">
                    客服
                    <el-button
                      type="info"
                      class="ml-auto"
                      @click="openTextarea(row, item.data)"
                      >修改</el-button
                    >
                  </h6>
                  <p>{{ item.data }}</p>
                </div>
              </template>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="ID" width="70" prop="id"></el-table-column>
      <el-table-column label="商品" width="200">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-image
              :src="row.goods_item ? row.goods_item.cover : ''"
              :lazy="true"
              style="width: 50px; height: 50px"
              class="rounded"
            ></el-image>
            <div class="ml-3">
              <h6>{{ row.goods_item?.title ?? "商品已被删除" }}</h6>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="评价信息">
        <template #default="{ row }">
          <div>
            <p>用户:{{ row.user?.username ?? "未知" }}</p>
            <p>
              <el-rate
                v-model="value"
                disabled
                show-score
                text-color="#ff9900"
                store-enplate="[5, 4, 3, 2, 1]"
              ></el-rate>
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="评价时间" width="180" prop="review_time" />
      <el-table-column label="状态" width="120">
        <template #default="{ row }">
          <el-switch
            :modelValue="row.status"
            :active-value="1"
            :inactive-value="0"
            :disabled="row.super == 1"
            :loading="row.statusLoading"
            @change="handleStatusChange($event, row)"
          >
          </el-switch>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="flex justify-center items-center mt-5">
      <el-pagination
        background
        layout="prev,pager,next"
        :total="total"
        v-model:current-page="currentPage"
        :page-size="limit"
        @current-change="getData"
      />
    </div>
  </el-card>
</template>

<script setup>
import { ref } from "vue";
import Search from "@/components/Search.vue";
import SearchItem from "@/components/SearchItem.vue";
import {
  getGoodsCommentList,
  updateGoodsCommentStatus,
  reviewGoodsComment,
} from "@/api/goods_comment.js";
import { useInitTable } from "@/utils/useCommon.js";
import { Eptoast } from "@/utils/util.js";
const roles = ref([]);

const {
  searchForm,
  resetSearchForm,
  tableData,
  loading,
  currentPage,
  total,
  limit,
  getData,
  handleDelete,
  handleStatusChange,
} = useInitTable({
  searchForm: {
    title: "",
  },
  getList: getGoodsCommentList,
  onGetListSuccess: (res) => {
    tableData.value = res.data.list.map((o) => {
      o.statusLoading = false;
      o.textareaEdit = false;
      return o;
    });
    total.value = res.data.totalCount;
    roles.value = res.data.roles;
  },
  updateStatus: updateGoodsCommentStatus,
});
const value = ref(0); // 根据实际情况初始化一个合适的值
const textarea = ref("");

const openTextarea = (row, data = "") => {
  textarea.value = data;
  row.textareaEdit = true;
};

const review = (row) => {
  if (textarea.value == "") {
    return Eptoast("回复内容不能为空", "error");
  }
  reviewGoodsComment(row.id, textarea.value).then((res) => {
    row.textareaEdit = false;
    Eptoast("回复成功", "success");
    getData();
  });
};
</script>
