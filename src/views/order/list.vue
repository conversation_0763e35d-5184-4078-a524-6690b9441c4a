<!--
 * @Author: yangy <EMAIL>
 * @Date: 2023-12-25 16:35:42
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-11-11 15:20:42
 * @FilePath: /admin_vue3_element_plus/src/views/notice/list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-tabs v-model="searchForm.tab" tab-position="top" @tab-change="getData">
      <el-tab-pane
        v-for="(item, index) in tabbars"
        :key="index"
        :label="item.name"
        :name="item.key"
      >
      </el-tab-pane>
    </el-tabs>

    <el-card shadow="never" class="border-0">
      <!-- 搜索部分 -->
      <!-- search 被触发之后调用getData函数 -->
      <Search :model="searchForm" @search="getData" @reset="resetSearchForm">
        <SearchItem label="关键词">
          <el-input
            v-model="searchForm.no"
            placeholder="订单编号"
            clearable
          ></el-input>
        </SearchItem>
        <template #show>
          <SearchItem label="收货人">
            <el-input
              v-model="searchForm.name"
              placeholder="收货人"
              clearable
            ></el-input>
          </SearchItem>
          <SearchItem label="手机号">
            <el-input
              v-model="searchForm.phone"
              placeholder="手机号"
              clearable
            ></el-input>
          </SearchItem>
          <SearchItem label="开始时间">
            <el-date-picker
              v-model="searchForm.starttime"
              type="date"
              placeholder="开始时间"
              style="width: 90%"
              value-format="YYYY-MM-DD"
            />
          </SearchItem>
          <SearchItem label="结束时间">
            <el-date-picker
              v-model="searchForm.endtime"
              type="date"
              placeholder="结束时间"
              style="width: 90%"
              value-format="YYYY-MM-DD"
            />
          </SearchItem>
        </template>
      </Search>
      <!-- 新增|刷新 -->
      <ListHeader
        layout="refresh,download"
        @refresh="getData"
        @download="handleExportExcel"
      >
        <el-button type="danger" size="small" @click="handleMultiDelete(1)"
          >批量删除</el-button
        >
      </ListHeader>

      <el-table
        :data="tableData"
        stripe
        style="width: 100%"
        v-loading="loading"
        ref="multipleTableRef"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="商品" width="300">
          <template #default="{ row }">
            <div>
              <div class="flex text-sm">
                <div class="flex-1">
                  <p>订单号</p>
                  <small>{{ row.no }}</small>
                </div>
                <div>
                  <p>下单时间</p>
                  <small>{{ row.create_time }}</small>
                </div>
              </div>
              <div
                class="flex py-2"
                v-for="(item, index) in row.order_items"
                :key="index"
              >
                <el-image
                  :src="item.goods_item ? item.goods_item.cover : ''"
                  fit="cover"
                  :lazy="true"
                  style="width: 30px; height: 30px"
                ></el-image>
                <p class="text-blue-500 ml-2">
                  {{ item.goods_item ? item.goods_item.title : "商品已被删除" }}
                </p>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="total_price"
          label="实际付款"
          width="120"
          align="center"
        />
        <el-table-column label="买家" align="center">
          <template #default="{ row }">
            <p>{{ row.user.nickname || row.user.username }}</p>
            <small>(用户ID:{{ row.user.id }})</small>
          </template>
        </el-table-column>
        <el-table-column label="交易状态" align="center">
          <template #default="{ row }">
            <div>
              付款状态:
              <el-tag
                v-if="row.payment_method == 'weixin'"
                type="success"
                size="small"
                >微信支付</el-tag
              >
              <el-tag
                v-else-if="row.payment_method == 'alipay'"
                type=""
                size="small"
                >支付宝支付</el-tag
              >
              <el-tag v-else type="info" size="small">未支付</el-tag>
            </div>
            <div>
              发货状态:
              <el-tag :type="row.ship_data ? 'success' : 'info'" size="small">{{
                row.ship_data ? "已发货" : "未发货"
              }}</el-tag>
            </div>
            <div>
              收货状态:
              <el-tag
                :type="row.ship_status == 'received' ? 'success' : 'info'"
                size="small"
                >{{
                  row.ship_status == "received" ? "已收货" : "未收货"
                }}</el-tag
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="{ row }">
            <el-button
              class="px-1"
              type="primary"
              size="small"
              text
              @click="openInfoModal(row)"
              >订单详情</el-button
            >
            <el-button
              v-if="searchForm.tab === 'noship'"
              class="px-1"
              type="primary"
              size="small"
              text
              @click="handleEdit(scope.row)"
              >订单发货</el-button
            >
            <el-button
              v-if="searchForm.tab === 'refunding'"
              class="px-1"
              type="primary"
              size="small"
              text
              @click="handleEdit(scope.row)"
              >同意退款</el-button
            >
            <el-button
              v-if="searchForm.tab === 'refunding'"
              class="px-1"
              type="primary"
              size="small"
              text
              @click="handleEdit(scope.row)"
              >拒绝退款</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="flex justify-center items-center mt-5">
        <el-pagination
          background
          layout="prev,pager,next"
          :total="total"
          v-model:current-page="currentPage"
          :page-size="limit"
          @current-change="getData"
        />
      </div>
    </el-card>

    <ExportExcel ref="ExportExcelRef" :tabs="tabbars" />
    <InfoModal ref="InfoModalRef" :info="info" />
  </div>
</template>

<script setup>
import { ref } from "vue";
import ListHeader from "@/components/ListHeader.vue";
import Search from "@/components/Search.vue";
import SearchItem from "@/components/SearchItem.vue";
import { Eptoast } from "@/utils/util";
import { getOrderList, deleteOrder } from "@/api/order.js";
import { getCategoryList } from "@/api/category";
import { useInitTable } from "@/utils/useCommon.js";
import ExportExcel from "./ExportExcel.vue";
import InfoModal from "./InfoModal.vue";

const {
  handleSelectionChange,
  handleMultiDelete,
  multipleTableRef,
  searchForm,
  resetSearchForm,
  tableData,
  loading,
  currentPage,
  total,
  limit,
  getData,
  handleDelete,
  multiSelectionIds,
} = useInitTable({
  searchForm: {
    no: "",
    tab: "all",
    starttime: "",
    endtime: "",
    name: "",
    phone: "",
  },
  getList: getOrderList,
  onGetListSuccess: (res) => {
    tableData.value = res.data.list.map((o) => {
      o.bannersLoading = false;
      o.contentLoading = false;
      o.skusLoading = false;
      return o;
    });
    total.value = res.data.totalCount;
  },
  delete: deleteOrder,
});

// 订单类型:all全部,nopay待支付,noship待发货,shiped 待收货,received 已收货,finish 已完成, closed 已关闭,refunding 退款中,refund 退款完成
const tabbars = [
  {
    key: "all",
    name: "全部",
  },
  {
    key: "nopay",
    name: "待支付",
  },
  {
    key: "noship",
    name: "待发货",
  },
  {
    key: "shiped",
    name: "待收货",
  },
  {
    key: "received",
    name: "已收货",
  },
  {
    key: "finish",
    name: "已完成",
  },
  {
    key: "closed",
    name: "已关闭",
  },
  {
    key: "refunding",
    name: "退款中",
  },
  // {
  //   key: "refund",
  //   name: "退款完成",
  // },
];

const handleEdit = () => {};

// 高级搜索框部分

// 导出excel
const ExportExcelRef = ref(null);
const handleExportExcel = () => {
  ExportExcelRef.value.open();
  //Eptoast("导出成功");
};

// 订单详情弹出层
const InfoModalRef = ref(null);
const info = ref(null);
const openInfoModal = (row) => {
  row.order_items = row.order_items.map((o) => {
    if (o.skus_type == 1 && o.goods_skus) {
      let s = [];
      for (let k in o.goods_skus.skus) {
        s.push(o.goods_skus.skus[k].value);
      }
      o.skus = s.join(",");
    }
    return o;
  });
  info.value = row;
  InfoModalRef.value.open();
};
</script>
