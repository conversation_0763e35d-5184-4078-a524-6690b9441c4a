<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-11-11 10:47:47
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-11-11 12:41:26
 * @FilePath: /admin_vue3_element_plus/src/views/order/ExportExcel.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-drawer title="导出订单" v-model="dialogVisible" size="40%">
    <el-form :model="form" label-width="80px">
      <el-form-item label="订单类型">
        <el-select v-model="form.tab" placeholder="请选择" clearable filterable>
          <el-option
            v-for="item in tabs"
            :key="item.key"
            :label="item.name"
            :value="item.key"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit" :loading="loading"
          >导出</el-button
        >
      </el-form-item>
    </el-form>
  </el-drawer>
</template>
<script setup>
import { time } from "echarts";
import { ref, reactive } from "vue";
import { exportOrder } from "@/api/order";
import { Eptoast } from "@/utils/util";
defineProps({
  tabs: Array,
});
const dialogVisible = ref(false);

const open = () => {
  dialogVisible.value = true;
};

const close = () => {
  dialogVisible.value = false;
};

const form = reactive({
  tab: null,
  time: "",
});
const loading = ref(false);
const onSubmit = () => {
  if (form.tab) {
    loading.value = true;
    let start_time = null;
    let end_time = null;

    if (form.time && Array.isArray(form.time)) {
      start_time = form.time[0];
      end_time = form.time[1];
    }
    exportOrder({ tab: form.tab, start_time, end_time })
      .then((res) => {
        let url = window.URL.createObjectURL(new Blob([res]));
        let link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        let filename = new Date().getTime() + "订单.xlsx";
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        close();
        // document.body.removeChild(link);
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    return Eptoast("请选择订单类型", "error");
  }
  dialogVisible.value = false;
};

defineExpose({
  open,
  close,
});
</script>
