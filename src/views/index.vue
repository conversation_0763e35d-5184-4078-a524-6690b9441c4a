<!--
 * @Author: yangy
 * @Date: 2023-12-12 13:12:45
 * @LastEditors: yangy
 * @LastEditTime: 2023-12-27 15:44:14
 * @FilePath: /admin_vue3_element_plus/src/views/index.vue
 * @Description: 
 * 
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved. 
-->

<template>
  <div>
    <!-- 四个大卡片统计 骨架屏 -->
    <el-row :gutter="20" v-permission="['getStatistics1,GET']">
      <template v-if="panels.length == 0">
        <el-col :span="6" v-for="i in 4" :key="i">
        <el-skeleton style="width: 100%" animated loading>
          <template #template>
            <el-card shadow="hover" class="border-0">
              <template #header>
                <div class="flex justify-between">
                  <el-skeleton-item variant="text" style="width:50%" />
                  <el-skeleton-item variant="text" style="width:10%" />
                </div>
              </template>
              <el-skeleton-item variant="h3" style="width:80%" />
              <el-divider/>
              <div class="flex justify-between text-sm text-gray-500">
                <el-skeleton-item variant="text" style="width:50%" />
                <el-skeleton-item variant="text" style="width:10%" />
              </div>
            </el-card>
          </template>
        </el-skeleton>
        </el-col>
      </template>
     
        <!-- 4个小卡片统计 实际显示 -->
      <el-col :span="6" :offset="0" v-for="(item,index) in panels" :key="index">
        <el-card shadow="hover" class="border-0">
          <template #header>
          <div class="flex justify-between">
            <span class="text-sm">{{ item.title }}</span>
            <el-tag :type="item.unitColor" effect="plain">
              {{ item.unit }}
            </el-tag>
          </div>
          </template>
          <!-- card body -->
          <span class="text-3xl font-bold text-gray-500">
            <CountTo :value="item.value"/>
          </span>

          <el-divider/>
          <div class="flex justify-between text-sm text-gray-500">
            <span>{{ item.subTitle }}</span>
            <span>{{ item.subValue }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 8个小卡片统计 -->
    <IndexNavs />
    <!-- 图表 echart -->
    <el-row :gutter="20" class="mt-5">
      <el-col :span="12" :offset="0"> 
        <IndexChart v-permission="['getStatistics3,GET']" />
      </el-col>
      <el-col :span="12" :offset="0" v-permission="['getStatistics2,GET']">
      <IndexCard title="店铺及商品提示" tip="店铺及商品提示" :btns="goods"/>
      <IndexCard class="mt-3" title="交易提示" tip="需要立即处理的交易订单" :btns="order"/>
      </el-col>
    </el-row>
    
  </div>
</template>

<script setup>
import {ref} from "vue"
import CountTo from '@/components/CountTo.vue'
import IndexNavs from '@/components/IndexNavs.vue'
import IndexChart from '@/components/IndexChart.vue'
import IndexCard from '@/components/IndexCard.vue'
import {getStatistic1,getStatistic2} from '@/api/index.js'

  const panels = ref([])
  const goods = ref([])
  const order = ref([])
  getStatistic1().then(res=>{
    panels.value = res.data.panels
    console.log(panels)
  })

  getStatistic2().then(res=>{
    console.log(res)
    goods.value = res.data.goods
    order.value = res.data.order
  })

</script>