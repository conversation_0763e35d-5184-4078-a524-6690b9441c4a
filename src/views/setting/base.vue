<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-27 17:15:53
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2025-06-27 16:04:36
 * @FilePath: /admin_vue3_element_plus/src/views/setting/base.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div v-loading="loading" class="bg-white p-4 rounded">
    <el-form :model="form" label-width="160px">
      <el-tabs v-model="activeName">
        <el-tab-pane label="注册与访问" name="first">
          <el-form-item label="是否允许注册会员">
            <el-radio-group v-model="form.open_reg">
              <el-radio :label="0" border>关闭</el-radio>
              <el-radio :label="1" border>开启</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="注册类型">
            <el-radio-group v-model="form.reg_method">
              <el-radio label="username" border>普通注册</el-radio>
              <el-radio label="phone" border>手机注册</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="密码最小长度">
            <el-input
              v-model="form.password_min"
              placeholder="密码最小长度"
              type="number"
              style="width: 30%"
            >
              <template #append> 位 </template>
            </el-input>
          </el-form-item>
          <el-form-item label="强制密码复杂度">
            <el-checkbox-group v-model="form.password_encrypt">
              <el-checkbox label="0" border>数字</el-checkbox>
              <el-checkbox label="1" border>小写字母</el-checkbox>
              <el-checkbox label="2" border>大写字母</el-checkbox>
              <el-checkbox label="3" border>特殊字符</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane label="上传设置" name="second">
          <el-form-item label="上传配置">
            <el-radio-group v-model="form.upload_method">
              <el-radio label="oss" border>对象存储</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="Bucket">
            <el-input
              v-model="form.upload_config.Bucket"
              placeholder="Bucket"
              style="width: 30%"
            />
          </el-form-item>
          <el-form-item label="ACCESS_KEY">
            <el-input
              v-model="form.upload_config.ACCESS_KEY"
              placeholder="ACCESS_KEY"
              style="width: 30%"
            />
          </el-form-item>
          <el-form-item label="SECRET_KEY">
            <el-input
              v-model="form.upload_config.SECRET_KEY"
              placeholder="SECRET_KEY"
              style="width: 30%"
            />
          </el-form-item>
          <el-form-item label="http">
            <el-input
              v-model="form.upload_config.http"
              placeholder="协议"
              style="width: 30%"
            />
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane label="Api安全" name="third">
          <el-form-item label="是否开启API安全">
            <el-radio-group v-model="form.api_safe">
              <el-radio :label="0" border>关闭</el-radio>
              <el-radio :label="1" border>开启</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="API密钥">
            <el-input
              v-model="form.api_secret"
              placeholder="API密钥"
              style="width: 30%"
            />
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { getSysConfig, setSysConfig } from "@/api/sysconfig";
import { onMounted, ref, reactive } from "vue";
import { Eptoast } from "@/utils/util";

const form = reactive({
  open_reg: 1,
  reg_method: "username",
  password_min: 7,
  password_encrypt: [],
  upload_method: "oss",
  upload_config: {
    Bucket: "",
    ACCESS_KEY: "",
    SECRET_KEY: "",
    http: "",
  },
  api_safe: 1,
  api_secret: "",
});

const activeName = ref("first");

const loading = ref(false);

function getData() {
  loading.value = true;
  getSysConfig()
    .then((res) => {
      for (let key in form) {
        form[key] = res.data[key];
      }
      form.password_encrypt = form.password_encrypt.split(",");
    })
    .finally(() => {
      loading.value = false;
    });
}

const onSubmit = () => {
  loading.value = true;
  setSysConfig({
    ...form,
    password_encrypt: form.password_encrypt.join(","),
  })
    .then((res) => {
      Eptoast("保存成功");
    })
    .finally(() => {
      loading.value = false;
    });
};

onMounted(() => {
  getData();
});
</script>

<style scoped lang="scss"></style>
