/*
 * @Author: yangy
 * @Date: 2023-12-12 13:03:34
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-23 11:33:57
 * @FilePath: /admin_vue3_element_plus/src/router/index.js
 * @Description:
 *
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved.
 */
import { createRouter, createWebHashHistory } from "vue-router";
import Index from "@/views/index.vue";
import Login from "@/views/login.vue";
import NotFound from "@/views/404.vue";
import Admin from "@/layouts/admin.vue";
import GoodsList from "@/views/goods/list.vue";
import CategoryList from "@/views/category/list.vue";
import UserList from "@/views/user/list.vue";
import OrderList from "@/views/order/list.vue";
import CommentList from "@/views/comment/list.vue";
import ImageList from "@/views/image/list.vue";
import NoticeList from "@/views/notice/list.vue";
import SettingBase from "@/views/setting/base.vue";
import CouponList from "@/views/coupon/list.vue";
import ManagerList from "@/views/manager/list.vue";
import AccessList from "@/views/access/list.vue";
import RoleList from "@/views/role/list.vue";
import SkusList from "@/views/skus/list.vue";
import LevelList from "@/views/level/list.vue";

// 默认路由,所有用户共享
const routes = [
  {
    path: "/",
    name: "admin",
    component: Admin,
  },
  {
    path: "/login",
    component: Login,
    meta: {
      title: "登录",
    },
  },
  // 404 路由
  { path: "/:pathMatch(.*)*", name: "NotFound", component: NotFound },
];

// 动态路由,用于匹配菜单动态添加路由的
const asyncRoutes = [
  {
    path: "/",
    name: "/",
    component: Index,
    meta: {
      title: "后台首页",
    },
  },
  {
    path: "/goods/list",
    name: "/goods/list",
    component: GoodsList,
    meta: {
      title: "商品列表",
    },
  },
  {
    path: "/category/list",
    name: "/category/list",
    component: CategoryList,
    meta: {
      title: "分类列表",
    },
  },
  {
    path: "/user/list",
    name: "/user/list",
    component: UserList,
    meta: {
      title: "用户列表",
    },
  },
  {
    path: "/order/list",
    name: "/order/list",
    component: OrderList,
    meta: {
      title: "订单列表",
    },
  },
  {
    path: "/comment/list",
    name: "/comment/list",
    component: CommentList,
    meta: {
      title: "评价列表",
    },
  },
  {
    path: "/image/list",
    name: "/image/list",
    component: ImageList,
    meta: {
      title: "图库列表",
    },
  },
  {
    path: "/notice/list",
    name: "/notice/list",
    component: NoticeList,
    meta: {
      title: "公告列表",
    },
  },
  {
    path: "/setting/base",
    name: "/setting/base",
    component: SettingBase,
    meta: {
      title: "配置",
    },
  },
  {
    path: "/coupon/list",
    name: "/coupon/list",
    component: CouponList,
    meta: {
      title: "优惠券列表",
    },
  },
  {
    path: "/manager/list",
    name: "/manager/list",
    component: ManagerList,
    meta: {
      title: "管理员管理",
    },
  },
  {
    path: "/access/list",
    name: "/access/list",
    component: AccessList,
    meta: {
      title: "菜单权限管理",
    },
  },
  {
    path: "/role/list",
    name: "/role/list",
    component: RoleList,
    meta: {
      title: "角色管理",
    },
  },
  {
    path: "/skus/list",
    name: "/skus/list",
    component: SkusList,
    meta: {
      title: "规格管理",
    },
  },
  {
    path: "/level/list",
    name: "/level/list",
    component: LevelList,
    meta: {
      title: "会员等级",
    },
  },
];

export const router = createRouter({
  // 4. 内部提供了 history 模式的实现。为了简单起见，我们在这里使用 hash 模式。
  history: createWebHashHistory(),
  routes, // `routes: routes` 的缩写
});

// 动态添加路由的方法
export function addRoutes(menus) {
  // 是否有新的路由
  let hasNewRoutes = false;
  const findAndAddRoutesByMenus = (arr) => {
    arr.forEach((e) => {
      let item = asyncRoutes.find((o) => o.path == e.frontpath);
      if (item && !router.hasRoute(item.path)) {
        router.addRoute("admin", item);
        hasNewRoutes = true;
      }
      if (e.child && e.child.length > 0) {
        findAndAddRoutesByMenus(e.child);
      }
    });
  };
  findAndAddRoutesByMenus(menus);
  // console.log(router.getRoutes);
  return hasNewRoutes;
}
