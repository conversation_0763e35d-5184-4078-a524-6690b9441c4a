/*
 * @Author: yangy <EMAIL>
 * @Date: 2024-08-12 16:12:03
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-22 16:09:47
 * @FilePath: /admin_vue3_element_plus/src/utils/useSku.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ref, nextTick, computed } from "vue";
import {
  createGoodsSkusCard,
  updateGoodsSkusCard,
  deleteGoodsSkusCard,
  sortGoodsSkusCard,
  createGoodsSkusCardValue,
  updateGoodsSkusCardValue,
  deleteGoodsSkusCardValue,
  chooseAndSetGoodsSkusCard,
} from "@/api/goods";
import {
  useArrayMoveUp,
  useArrayMoveDown,
  cartesianProductOf,
} from "@/utils/util";

// 当前商品ID
export const goodsId = ref(0);

// 规格选项列表
export const sku_card_list = ref([]);

export const sku_list = ref([]);
// 初始化规格选项列表
export const initSkuCardList = (d) => {
  sku_card_list.value = d.goodsSkusCard.map((item) => {
    item.text = item.name; // 增加text这个属性, 目的是万一修改失败则可以恢复原状
    item.loading = false; // 为后期的loading值做准备,增加loading防止用户重复提交
    item.goodsSkusCardValue.map((v) => {
      v.text = v.value || "属性值";
      return v;
    });
    return item;
  });
  sku_list.value = d.goodsSkus;
};

// 添加规格选项
export const btnLoading = ref(false);
export const addSkuCardEvent = () => {
  btnLoading.value = true;
  createGoodsSkusCard({
    goods_id: goodsId.value,
    name: "规格选项",
    order: 50,
    type: 0,
  })
    .then((res) => {
      sku_card_list.value.push({
        ...res,
        text: res.name,
        loading: false,
        goodsSkusCardValue: [],
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

// 修改规格选项
export const updateSkuCardEvent = (item) => {
  item.loading = true;
  updateGoodsSkusCard(item.id, {
    goods_id: item.goods_id,
    name: item.text,
    order: item.order,
    type: 0,
  })
    .then((res) => {
      item.name = item.text;
    })
    .catch((err) => {
      item.text = item.name;
    })
    .finally(() => {
      item.loading = false;
    });
};

// 删除规格选项
export const handleDelete = (item) => {
  item.loading = true;
  deleteGoodsSkusCard(item.id)
    .then((res) => {
      // 首先找到要删除对象的索引
      const i = sku_card_list.value.findIndex((o) => o.id == item.id);
      // 如果索引不是-1 则进行删除
      if (i != -1) {
        sku_card_list.value.splice(i, 1);
      }
      // 重新渲染
      getTableData();
      // 或者 方法二
      //
      // sku_card_list.value = sku_card_list.value.filter((o) => o.id !== id);
    })
    .finally(() => {});
  //
};

// 排序规格选项
export const bodyLoading = ref(false);
export const sortCard = (action, index) => {
  let oList = JSON.parse(JSON.stringify(sku_card_list.value));

  let func = action == "up" ? useArrayMoveUp : useArrayMoveDown;

  let sortData = oList.map((o, i) => {
    return { id: o.id, order: i + 1 };
  });
  bodyLoading.value = true;
  sortGoodsSkusCard({
    sortdata: sortData,
  })
    .then(() => {
      func(sku_card_list.value, index);
      // 重新渲染
      getTableData();
    })
    .finally(() => {
      bodyLoading.value = false;
    });
};
// 选择设置规格
export function handleChooseSetGoodsSkusCard(id, data) {
  let item = sku_card_list.value.find((o) => o.id == id);
  item.loading = true;
  chooseAndSetGoodsSkusCard(id, data)
    .then((res) => {
      item.name = item.text = res.goods_skus_card?.name;
      item.goodsSkusCardValue = res.goods_skus_card_value?.map((o) => {
        o.text = o.value || "属性值";
        return o;
      });
      // 重新渲染
      getTableData();
    })
    .finally(() => {
      item.loading = false;
    });
}

// 初始化规格的值
export const initSkusCardItem = (id) => {
  const item = sku_card_list.value.find((o) => o.id === id);
  const inputValue = ref("");
  const loading = ref(false);
  const inputVisible = ref(false);
  const InputRef = ref();

  const handleClose = (tag) => {
    loading.value = true;
    // 请求接口删除
    deleteGoodsSkusCardValue(tag.id)
      .then(() => {
        // dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1);
        let i = item.goodsSkusCardValue.findIndex((o) => o.id === tag.id);
        if (i != -1) {
          item.goodsSkusCardValue.splice(i, 1);
        }
        // 重新渲染
        getTableData();
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const showInput = () => {
    inputVisible.value = true;
    nextTick(() => {
      InputRef.value.input.focus();
    });
  };

  const handleInputConfirm = () => {
    if (!inputValue.value) {
      inputVisible.value = false;
      return;
    }
    loading.value = true;

    createGoodsSkusCardValue({
      goods_skus_card_id: id,
      name: item.name,
      order: 50,
      value: inputValue.value,
    })
      .then((res) => {
        item.goodsSkusCardValue.push({
          ...res.data,
          // text: res.data.value || "属性值",
          text: res.data.value,
          // loading: false,
        });
        // 重新渲染
        getTableData();
      })
      .finally(() => {
        loading.value = false;
        inputVisible.value = false;
        inputValue.value = "";
      });

    // 失去焦点或者按回车 则会向dynamicTags push一个值
    // if (inputValue.value) {
    //   dynamicTags.value.push(inputValue.value);
    // }
    inputVisible.value = false;
    inputValue.value = "";
  };
  const handleChange = (value, tag) => {
    // 这个value就是修改之后的值
    // handleCurrentChange;
    loading.value = true;
    updateGoodsSkusCardValue(tag.id, {
      goods_skus_card_id: id,
      name: item.name, //规格选项名称
      order: 50, // 排序
      value: value, // 规格选项值的名称
    })
      .then(() => {
        tag.value = value;
        getTableData();
      })
      .catch((err) => {
        tag.text = tag.value;
      })
      .finally(() => {
        loading.value = false;
      });
  };
  // 删除规格选项值
  const deleteSkusCardValue = (id) => {
    loading.value = true;
    deleteGoodsSkusCardValue(id);
  };
  return {
    item,
    inputValue,
    inputVisible,
    InputRef,
    loading,
    handleClose,
    showInput,
    handleInputConfirm,
    handleChange,
  };
};

// 初始化表格

export function initSkuTable() {
  const skuLabels = computed(() =>
    sku_card_list.value.filter((v) => v.goodsSkusCardValue?.length > 0)
  );
  // 获取表头的数据
  const tableThs = computed(() => {
    let length = skuLabels.value.length;

    return [
      {
        name: "商品规格",
        colspan: length,
        width: "",
        rowspan: length > 0 ? 1 : 2,
      },
      { name: "销售价", width: "100px", rowspan: 2 },
      { name: "市场价", width: "100px", rowspan: 2 },
      { name: "成本价", width: "100px", rowspan: 2 },
      { name: "库存", width: "100px", rowspan: 2 },
      { name: "体积", width: "100px", rowspan: 2 },
      { name: "重量", width: "100px", rowspan: 2 },
      { name: "编码", width: "100px", rowspan: 2 },
    ];
  });

  return {
    skuLabels,
    tableThs,
    sku_list,
  };
}

// 获取规格表格数据
// 作用: 当操作规格选项或者规格值的时候,或者排序的时候重新调用这个方法,重新生成对应的数据
// 从而重新渲染
function getTableData() {
  setTimeout(() => {
    if (sku_card_list.value.length === 0) {
      return [];
    }
    let list = [];
    sku_card_list.value.forEach((o) => {
      // o.goodsSkusCardValue
      if (o.goodsSkusCardValue && o.goodsSkusCardValue.length > 0) {
        list.push(o.goodsSkusCardValue);
      }
    });
    if (list.length == 0) {
      return [];
    }
    // 这个就是排列组合后的数据
    let arr = cartesianProductOf(...list);
    // 获取之前的规格列表,将规格ID排序之后转化成字符串
    let beforeSkuList = JSON.parse(JSON.stringify(sku_list.value)).map((o) => {
      // o.skusId="290-282"
      if (!Array.isArray(o.skus)) {
        o.skus = Object.keys(o.skus).map((k) => {
          o.skus[k];
        });
        // o.skus.sort((a, b) => a.id - b.id).map((s) => s.id); // 到这一步 就会生成[290,282] 这样的数组
        o.skusId = o.skus
          .sort((a, b) => a.id - b.id)
          .map((s) => s.id)
          .join(",");

        return o;
      }
    });
    sku_list.value = [];
    sku_list.value = arr.map((skus) => {
      // 判断之前有没有
      let o = getBeforeSkuItem(JSON.parse(JSON.stringify(skus)), beforeSkuList);
      return {
        code: o?.code || "",
        cprice: o?.cprice || "0.00",
        goods_id: goodsId.value,
        image: o?.image || "",
        oprice: o?.oprice || "0.00",
        pprice: o?.pprice || "0.00",
        skus: o,
        stock: o?.stock || 0,
        volume: o?.volume || 0,
        weight: o?.weight || 0,
      };
    });
    sku_list.value = arr;
  }, 200);
}

function getBeforeSkuItem(skus, beforeSkuList) {
  let skusId = skus
    .sort((a, b) => a.id - b.id)
    .map((s) => s.id)
    .join(",");
  return beforeSkuList.find((o) => {
    if (skus.length > o.skus.length) {
      return skusId.indexOf(o.skusId) != -1;
    }
    return o.skusId.indexOf(skusId) != -1;
  });
}
