/*
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-28 16:22:13
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-22 17:24:25
 * @FilePath: /admin_vue3_element_plus/src/utils/useCommon.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import { ref, reactive, computed } from "vue";
import { Eptoast } from "@/utils/util.js";
// 列表,分页,搜索
export function useInitTable(opt = {}) {
  let searchForm = null;
  let resetSearchForm = null;

  if (opt.searchForm) {
    searchForm = reactive({ ...opt.searchForm });
    resetSearchForm = () => {
      for (let key in opt.searchForm) {
        searchForm[key] = opt.searchForm[key];
      }
      getData();
    };
  }

  const tableData = ref([]);
  const loading = ref(false);

  // 分页
  const currentPage = ref(1);
  const total = ref(0);
  const limit = ref(10);
  //获取数据 p是接收current-change传过来的当前页码
  function getData(p = null) {
    if (typeof p == "number") {
      currentPage.value = p;
    }
    loading.value = true;
    // 获取数据的时候传当前页的页码
    opt
      .getList(currentPage.value, searchForm)
      .then((res) => {
        if (opt.onGetListSuccess && typeof opt.onGetListSuccess == "function") {
          opt.onGetListSuccess(res);
        } else {
          tableData.value = res.data.list;
          total.value = res.data.totalCount;
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  // 删除
  const handleDelete = (id) => {
    loading.value = true;
    opt
      .delete(id)
      .then((res) => {
        Eptoast("删除成功");
        getData(); // 删除成功后刷新本页
      })
      .finally(() => {
        loading.value = false;
      });
  };

  // 修改状态
  const handleStatusChange = (status, row) => {
    row.handleStatusChange = true;
    opt
      .updateStatus(row.id, status)
      .then((res) => {
        Eptoast("修改状态成功!");
        row.status = status;
      })
      .finally(() => {
        row.handleStatusChange = false;
      });
  };

  getData();

  // 多选  选中id
  const multiSelectionIds = ref([]);
  const handleSelectionChange = (e) => {
    multiSelectionIds.value = e.map((o) => o.id);
  };

  // 批量删除的方法
  const multipleTableRef = ref(null);
  const handleMultiDelete = () => {
    loading.value = true;
    opt
      .delete(multiSelectionIds.value)
      .then((res) => {
        Eptoast("删除成功");
        // 清空选中
        if (multipleTableRef.value) {
          multipleTableRef.value.clearSelection();
        }
        getData();
      })
      .finally(() => {
        loading.value = false;
      });
  };

  // 批量修改状态的方法
  const handleMultiStatusChange = (status) => {
    loading.value = true;
    opt
      .updateStatus(multiSelectionIds.value, status)
      .then((res) => {
        Eptoast("修改状态成功");
        // 清空选中
        if (multipleTableRef.value) {
          multipleTableRef.value.clearSelection();
        }
        getData();
      })
      .finally(() => {
        loading.value = false;
      });
  };

  return {
    searchForm,
    resetSearchForm,
    tableData,
    loading,
    currentPage,
    total,
    limit,
    getData,
    handleDelete,
    handleStatusChange,
    handleMultiDelete,
    multipleTableRef,
    handleSelectionChange,
    handleMultiStatusChange,
    multiSelectionIds,
  };
}

// 新增,修改
export function useInitForm(opt = {}) {
  // 表单部分
  const formDrawerRef = ref(null);
  const formRef = ref(null);
  const form = reactive({ ...opt.form });

  const rules = opt.rules || {};
  const editId = ref(0);
  const drawerTitle = computed(() => (editId.value ? "修改" : "新增"));
  // 新增
  const handleSubmit = () => {
    formRef.value.validate((valid) => {
      if (!valid) {
        return;
      }
      // 提交按钮的loading状态
      formDrawerRef.value.showLoading();
      let body = {};
      if (opt.beforeSubmit && typeof opt.beforeSubmit == "function") {
        body = opt.beforeSubmit({ ...form });
      } else {
        body = form;
      }
      const fun = editId.value
        ? opt.update(editId.value, body)
        : opt.create(body);
      fun
        .then((res) => {
          Eptoast(drawerTitle.value + "成功");
          // 修改刷新当前页,新增刷新第一页
          opt.getData(editId.value ? false : 1);
          formDrawerRef.value.close();
        })
        .finally(() => {
          formDrawerRef.value.hideLoading();
        });
    });
  };
  //重置表单
  function resetForm(row = false) {
    if (formRef.value) {
      formRef.value.clearValidate();
    }
    // 这样不用重新请求编辑接口,将该对象重新赋值了
    if (row) {
      for (let key in form) {
        form[key] = row[key];
      }
    }
  }

  // 打开抽屉组件
  const handleCreate = () => {
    editId.value = 0; //区分是新增还是修改
    // 重置form表单
    resetForm(opt.form); //重置表单项
    formDrawerRef.value.open();
  };
  // 修改
  const handleEdit = (row) => {
    editId.value = row.id;
    resetForm(row);
    formDrawerRef.value.open();
  };

  return {
    formDrawerRef,
    formRef,
    form,
    rules,
    editId,
    drawerTitle,
    handleSubmit,
    resetForm,
    handleCreate,
    handleEdit,
  };
}
