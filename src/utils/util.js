/*
 * @Author: yangy
 * @Date: 2023-12-14 15:23:33
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-22 10:52:17
 * @FilePath: /admin_vue3_element_plus/src/utils/util.js
 * @Description:
 *
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved.
 */
import { ElNotification, ElMessageBox } from "element-plus";
import nprogress from "nprogress";

export function Eptoast(
  message,
  type = "success",
  dangerouslyUseHTMLString = true,
  duration = 3000
) {
  ElNotification({
    message,
    type,
    dangerouslyUseHTMLString,
    duration,
  });
}

export function showModal(content = "提示内容", type = "warning", title = "") {
  return ElMessageBox.confirm(content, title, {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type,
  });
}

// 显示全屏loading
export function showFullLoading() {
  nprogress.start();
}

// 隐藏全屏loading
export function hideFullLoading() {
  nprogress.done();
}

// 弹出输入框
export function showPrompt(tip, value = "") {
  return ElMessageBox.prompt(tip, "", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    inputValue: value,
  });
}

// 将query对象转成url参数
export function queryParams(query) {
  let q = [];
  for (let key in query) {
    if (query[key]) {
      q.push(`${key}=${encodeURIComponent(query[key])}`);
    }
  }
  let r = q.join("&");
  r = r ? "?" + r : "";

  return r;
}

// 排序
// 上移的方法

export function useArrayMoveUp(arr, index) {
  swapArray(arr, index, index - 1);
}

// 下移的方法
export function useArrayMoveDown(arr, index) {
  swapArray(arr, index, index + 1);
}

// 这方法很好, 用于移动数组中的元素,index1和index2在arr数组中的位置互换
function swapArray(arr, index1, index2) {
  arr[index1] = arr.splice(index2, 1, arr[index1])[0];
  return arr;
}

// sku 排列算法

export function cartesianProductOf() {
  return Array.prototype.reduce.call(
    arguments,
    function (a, b) {
      var ret = [];
      a.forEach(function (a) {
        b.forEach(function (b) {
          ret.push(a.concat([b]));
        });
      });
      return ret;
    },
    [[]]
  );
}
