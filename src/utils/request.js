/*
 * @Author: yangy
 * @Date: 2023-11-06 11:32:48
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-11-11 12:22:38
 * @FilePath: /admin_vue3_element_plus/src/utils/request.js
 * @Description:
 *
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved.
 */
// 1. 创建一个新的axios实例
// 2. 请求拦截器，如果有token进行头部携带
// 3. 响应拦截器：1. 剥离无效数据  2. 处理token失效
// 4. 导出一个函数，调用当前的axsio实例发请求，返回值promise

import axios from "axios";
// import { useUserStore } from "@/store/user.js";
import { router } from "@/router";
import { getToken } from "@/utils/auth";
import { Eptoast } from "@/utils/util";

// 导出基准地址
// export const baseURL = 'http://pcapi-xiaotuxian-front-devtest.itheima.net'
// export const baseURL = 'https://apipc-xiaotuxian-front.itheima.net'
export const baseURL = "/api";

// const userStore = useUserStore();

const instance = axios.create({
  // axios 的一些配置，baseURL  timeout
  baseURL,
  timeout: 5000, // 5s
});
// interceptors:拦截器
// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 拦截业务逻辑
    // 进行请求配置的修改
    // 如果本地又token就在头部携带
    // 1. 获取用户信息对象
    // 往header头自动添加token
    const token = getToken();
    // const token = userStore.token;
    // const token = "";
    // 2. 判断是否有token
    if (token) {
      // 3. 设置token
      config.headers["token"] = token;
    }
    return config;
  },
  (err) => {
    return Promise.reject(err);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  // res => res.data  取出data数据，将来调用接口的时候直接拿到的就是后台的数据
  (res) => (res?.request?.responseType === "blob" ? res : res.data), // 用于数据导出
  (err) => {
    // 这个 response.status是指网络错误
    if (err.response && err.response.status === 401) {
      // 1. 清空无效用户信息
      // 2. 跳转到登录页
      // 3. 跳转需要传参（当前路由地址）给登录页码
      // store.commit('user/setUser', {})
      // 当前路由地址
      // 组件里头：`/user?a=10` $route.path === /user  $route.fullPath === /user?a=10
      // js模块中：router.currentRoute.value.fullPath 就是当前路由地址，router.currentRoute 是ref响应式数据
      const fullPath = encodeURIComponent(router.currentRoute.value.fullPath);
      // encodeURIComponent 转换uri编码，防止解析地址出问题
      router.push("/login?redirectUrl=" + fullPath);
    }
    // 失败提示
    Eptoast(err.response.data.msg || "请求失败!", "error");
    // return Promise.reject(new Error(err.msg))
    return Promise.reject(err);
  }
);

// export default instance;

// 请求工具函数
export default (url, method, submitData) => {
  // 负责发请求：请求地址，请求方式，提交的数据
  return instance({
    url,
    method,
    // 1. 如果是get请求  需要使用params来传递submitData   ?a=10&c=10
    // 2. 如果不是get请求  需要使用data来传递submitData   请求体传参
    // [] 设置一个动态的key, 写js表达式，js表达式的执行结果当作KEY
    // method参数：get,Get,GET  转换成小写再来判断
    // 在对象，['params']:submitData ===== params:submitData 这样理解
    [method.toLowerCase() === "get" ? "params" : "data"]: submitData,
  });
};
