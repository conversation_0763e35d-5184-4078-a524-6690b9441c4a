/*
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-27 11:51:21
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-23 11:36:58
 * @FilePath: /admin_vue3_element_plus/src/api/role.js
 */
import request from "@/utils/request";

export function getUserLevelList(page) {
  return request(`/admin/user_level/${page}`, "get");
}

// 增加公告
export function createUserLevel(data) {
  return request("/admin/user_level", "post", data);
}

//更新
export function updateUserLevel(id, data) {
  return request(`/admin/user_level/${id}`, "post", data);
}

//删除
export function deleteUserLevel(id) {
  return request(`/admin/user_level/${id}/delete`, "post");
}

export function updateUserLevelStatus(id, status) {
  return request(`/admin/user_level/${id}/update_status`, "post", { status });
}
