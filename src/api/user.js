/*
 * @Author: yangy
 * @Date: 2023-12-13 16:41:50
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-23 12:36:26
 * @FilePath: /admin_vue3_element_plus/src/api/manager.js
 * @Description: 管理员模块
 *
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved.
 */
// import axios from "@/axios";

import request from "@/utils/request";
import { queryParams } from "@/utils/util";

export function getUserList(page, query = {}) {
  let r = queryParams(query);
  return request(`/admin/user/${page}${r}`, "get");
}

export function updateUserStatus(id, status) {
  return request(`/admin/user/${id}/update_status`, "post", { status });
}

// 新增

export function createUser(data) {
  return request(`/admin/user`, "post", data);
}

//修改
export function updateUser(id, data) {
  return request(`/admin/user/${id}`, "post", data);
}
//删除
export function deleteUser(id) {
  return request(`/admin/user/${id}/delete`, "post");
}
