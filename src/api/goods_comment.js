/*
 * @Author: yangy
 * @Date: 2023-12-13 16:41:50
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-27 15:52:12
 * @FilePath: /admin_vue3_element_plus/src/api/manager.js
 * @Description: 评论区模块
 *
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved.
 */
// import axios from "@/axios";

import request from "@/utils/request";
import { queryParams } from "@/utils/util";

export function getGoodsCommentList(page, query = {}) {
  let r = queryParams(query);
  return request(`/admin/goods_comment/${page}${r}`, "get");
}

// 修改评论状态
export function updateGoodsCommentStatus(id, status) {
  return request(`/admin/goods_comment/${id}/update_status`, "post", {
    status,
  });
}

export function reviewGoodsComment(id, data) {
  return request(`/admin/goods_comment/review/${id}`, "post", {
    data,
  });
}
