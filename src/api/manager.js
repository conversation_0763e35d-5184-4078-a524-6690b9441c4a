/*
 * @Author: yangy
 * @Date: 2023-12-13 16:41:50
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-05-28 11:51:11
 * @FilePath: /admin_vue3_element_plus/src/api/manager.js
 * @Description: 管理员模块
 *
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved.
 */
// import axios from "@/axios";

import request from "@/utils/request";
import { queryParams } from "@/utils/util";

// 登录
export const login = (username, password) => {
  return request("/admin/login", "post", { username, password });
};

// 获取用户信息
export const getInfo = () => {
  return request("/admin/getinfo", "post", {});
};

// 退出登录
export const logout = () => {
  return request("/admin/logout", "post", {});
};

// 修改密码
export const updatePassword = (data) => {
  return request("/admin/updatepassword", "post", data);
};

export function getManagerList(page, query = {}) {
  let r = queryParams(query);
  return request(`/admin/manager/${page}${r}`, "get");
}

export function updateManagerStatus(id, status) {
  return request(`/admin/manager/${id}/update_status`, "post", { status });
}

// 新增

export function createManager(data) {
  return request(`/admin/manager`, "post", data);
}

//修改
export function updateManager(id, data) {
  return request(`/admin/manager/${id}`, "post", data);
}
//删除
export function deleteManager(id) {
  return request(`/admin/manager/${id}/delete`, "post");
}
