/*
 * @Author: yangy
 * @Date: 2023-12-13 16:41:50
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-22 17:31:54
 * @FilePath: /admin_vue3_element_plus/src/api/Goods.js
 * @Description: 管理员模块
 *
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved.
 */
// import axios from "@/axios";

import request from "@/utils/request";
import { queryParams } from "@/utils/util";

export function getGoodsList(page, query = {}) {
  let r = queryParams(query);
  return request(`/admin/goods/${page}${r}`, "get");
}

// 更新产品状态 批量上架下架
export function updateGoodsStatus(ids, status) {
  return request(`/admin/goods/changestatus`, "post", {
    ids,
    status,
  });
}

// 新增商品

export function createGoods(data) {
  return request(`/admin/goods`, "post", data);
}

//修改商品
export function updateGoods(id, data) {
  return request(`/admin/goods/${id}`, "post", data);
}
//删除
export function deleteGoods(ids) {
  ids = !Array.isArray(ids) ? [ids] : ids;
  return request(`/admin/goods/delete_all`, "post", {
    ids,
  });
}

export function restoreGoods(ids) {
  ids = !Array.isArray(ids) ? [ids] : ids;
  return request(`/admin/goods/restore`, "post", {
    ids,
  });
}

export function destroyGoods(ids) {
  ids = !Array.isArray(ids) ? [ids] : ids;
  return request(`/admin/goods/destroy`, "post", {
    ids,
  });
}

export function readGoods(id) {
  return request(`/admin/goods/read/${id}`, "get");
}

export function setGoodsBanner(id, data) {
  return request(`/admin/goods/banners/${id}`, "post", data);
}

export function updateGoodsSkus(id, data) {
  return request(`/admin/goods/updateskus/${id}`, "post", data);
}

export function createGoodsSkusCard(data) {
  return request(`/admin/goods_skus_card`, "post", data);
}

export function updateGoodsSkusCard(id, data) {
  return request(`/admin/goods_skus_card/${id}`, "post", data);
}

export function deleteGoodsSkusCard(id) {
  return request(`/admin/goods_skus_card/${id}/delete`, "post");
}

export function sortGoodsSkusCard(data) {
  return request(`/admin/goods_skus_card/sort`, "post", data);
}

export function createGoodsSkusCardValue(data) {
  return request(`/admin/goods_skus_card_value`, "post", data);
}

export function updateGoodsSkusCardValue(id, data) {
  return request(`/admin/goods_skus_card_value/${id}`, "post", data);
}

export function deleteGoodsSkusCardValue(id) {
  return request(`/admin/goods_skus_card_value/${id}/delete`, "post");
}

export function chooseAndSetGoodsSkusCard(id, data) {
  return request(`/admin/goods_skus_card/${id}/set`, "post", data);
}
