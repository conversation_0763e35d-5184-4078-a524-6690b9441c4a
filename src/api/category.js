/*
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-27 11:51:21
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-05-07 17:42:35
 * @FilePath: /admin_vue3_element_plus/src/api/role.js
 */
import request from "@/utils/request";

export function getCategoryList() {
  return request(`/admin/category`, "get");
}

// 增加公告
export function createCategory(data) {
  return request("/admin/category", "post", data);
}

//更新
export function updateCategory(id, data) {
  return request(`/admin/category/${id}`, "post", data);
}

//删除
export function deleteCategory(id) {
  return request(`/admin/category/${id}/delete`, "post");
}

export function updateCategoryStatus(id, status) {
  return request(`/admin/category/${id}/update_status`, "post", { status });
}

export function getCategoryGoods(id) {
  return request(`/admin/app_category_item/list?category_id=${id}`, "get");
}

// 删除关联商品

export function deleteCategoryGoods(id) {
  return request(`/admin/app_category_item/${id}/delete`, "post");
}

export function connectCategoryGoods() {
  return "";
}
