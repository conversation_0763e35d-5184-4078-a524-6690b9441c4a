/*
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-27 11:51:21
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-03-29 12:47:24
 * @FilePath: /admin_vue3_element_plus/src/api/role.js
 */
import request from "@/utils/request";

export function getRoleList(page) {
  return request(`/admin/role/${page}`, "get");
}

// 增加公告
export function createRole(data) {
  return request("/admin/role", "post", data);
}

//更新
export function updateRole(id, data) {
  return request(`/admin/role/${id}`, "post", data);
}

//删除
export function deleteRole(id) {
  return request(`/admin/role/${id}/delete`, "post");
}

export function updateRoleStatus(id, status) {
  return request(`/admin/role/${id}/update_status`, "post", { status });
}

//设置角色权限
export function setRoleRules(id, rule_ids) {
  return request(`/admin/role/set_rules`, "post", { id, rule_ids });
}
