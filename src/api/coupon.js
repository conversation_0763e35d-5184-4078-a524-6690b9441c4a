/*
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-27 11:51:21
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-04-12 15:50:18
 * @FilePath: /admin_vue3_element_plus/src/api/coupon.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "@/utils/request";

export function getCouponList(page) {
  return request(`/admin/coupon/${page}`, "get");
}

// 增加公告
export function createCoupon(data) {
  return request("/admin/coupon", "post", data);
}

//更新
export function updateCoupon(id, data) {
  return request(`/admin/coupon/${id}`, "post", data);
}

//删除
export function deleteCoupon(id) {
  return request(`/admin/coupon/${id}/delete`, "post");
}

//更新状态
export function updateCouponStatus(id) {
  return request(`/admin/coupon/${id}/update_status`, "post", { status: 0 });
}
