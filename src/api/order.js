/*
 * @Author: yangy
 * @Date: 2023-12-13 16:41:50
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-11-11 12:21:28
 * @FilePath: /admin_vue3_element_plus/src/api/Goods.js
 * @Description: 管理员模块
 *
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved.
 */
// import axios from "@/axios";

import request from "@/utils/request";
import { queryParams } from "@/utils/util";

export function getOrderList(page, query = {}) {
  let r = queryParams(query);
  return request(`/admin/order/${page}${r}`, "get");
}

//删除
export function deleteOrder(ids) {
  ids = !Array.isArray(ids) ? [ids] : ids;
  return request(`/admin/order/delete_all`, "post", {
    ids,
  });
}

export function exportOrder(query = {}) {
  let r = queryParams(query);
  return request(
    `/admin/order/excelexport${r}`,
    "post",
    {},
    {
      responseType: "blob",
    }
  );
}
