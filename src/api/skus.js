/*
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-27 11:51:21
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-20 15:02:30
 * @FilePath: /admin_vue3_element_plus/src/api/Skus.js
 */
import request from "@/utils/request";

export function getSkusList(page) {
  return request(`/admin/skus/${page}`, "get");
}

// 增加公告
export function createSkus(data) {
  return request("/admin/skus", "post", data);
}

//更新
export function updateSkus(id, data) {
  return request(`/admin/skus/${id}`, "post", data);
}

//删除
export function deleteSkus(ids) {
  ids = !Array.isArray(ids) ? [ids] : ids;
  return request(`/admin/skus/delete_all`, "post", { ids });
}

export function updateSkusStatus(id, status) {
  return request(`/admin/skus/${id}/update_status`, "post", { status });
}
