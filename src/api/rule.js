/*
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-28 16:00:10
 * @LastEditors: vinbrave <EMAIL>
 * @LastEditTime: 2024-02-29 20:02:20
 * @FilePath: /admin_vue3_element_plus/src/api/rule.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "@/utils/request";

export function getRuleList(page) {
  return request(`/admin/rule/${page}`, "get");
}

// 增加公告
export function createRule(data) {
  return request("/admin/rule", "post", data);
}

//更新
export function updateRule(id, data) {
  return request(`/admin/rule/${id}`, "post", data);
}
//删除
export function deleteRule(id) {
  return request(`/admin/rule/${id}/delete`, "post");
}

export function updateRuleStatus(id, status) {
  return request(`/admin/rule/${id}/update_status`, "post", { status });
}
