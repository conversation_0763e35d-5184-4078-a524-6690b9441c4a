/*
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-27 11:51:21
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2025-06-27 14:52:42
 * @FilePath: /admin_vue3_element_plus/src/api/sysconfig.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "@/utils/request";

export function getSysConfig() {
  return request(`/admin/sysconfig`, "get");
}

export function setSysConfig(data) {
  return request(`/admin/sysconfig`, "post", data);
}

// 增加公告
export function createSysConfig(data) {
  return request("/admin/sysconfig", "post", data);
}

//更新
export function updateSysConfig(id, data) {
  return request(`/admin/sysconfig/${id}`, "post", data);
}

//删除
export function deleteSysConfig(id) {
  return request(`/admin/sysconfig/${id}/delete`, "post");
}
