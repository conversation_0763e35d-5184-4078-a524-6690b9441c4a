<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-28 10:46:00
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-21 12:26:53
 * @FilePath: /admin_vue3_element_plus/src/components/ChooseImage.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div v-if="modelValue && preview">
    <el-image
      v-if="typeof modelValue == 'string'"
      :src="modelValue"
      fit="cover"
      class="w-[100px] h-[100px] rounded border mr-2"
    >
    </el-image>
    <div v-else class="flex flex-wrap">
      <div
        class="relative mx-1 mb-2 w-[100px] h-[100px]"
        v-for="(url, index) in modelValue"
        :key="index"
      >
        <el-icon
          class="absolute right-[5px] top-[5px] cursor-pointer bg-white rounded-full"
          style="z-index: 10"
          @click="removeImage(url)"
          ><CircleClose
        /></el-icon>
        <el-image
          :src="url"
          fit="cover"
          class="w-[100px] h-[100px] rounded border mr-2"
        >
        </el-image>
      </div>
    </div>
  </div>
  <div v-if="preview" class="choose-image-btn" @click="open">
    <el-icon :size="25" class="text-gray-500"><Plus /></el-icon>
  </div>
  <el-dialog title="选择图片" v-model="dialogVisible" width="80%" top="5vh">
    <!-- dialog 弹窗内容 样式 start-->
    <el-container class="bg-white rounded" style="height: 70vh">
      <el-header class="image-header">
        <el-button type="primary" size="small" @click="handleOpenCreate"
          >新增图片分类</el-button
        >
        <el-button type="warning" size="small" @click="handleOpenUpload"
          >上传图片</el-button
        >
      </el-header>
      <el-container>
        <!-- 侧边栏 -->
        <ImageAside ref="ImageAsideRef" @change="handleAsideChange" />
        <!-- 主体部分 -->
        <ImageMain
          ref="ImageMainRef"
          @choose="handleChoose"
          openChoose
          :limit="limit"
        />
      </el-container>
    </el-container>
    <!-- dialog 弹窗内容 样式 end -->
    <template #footer>
      <span>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import ImageAside from "@/components/ImageAside.vue";
import ImageMain from "@/components/ImageMain.vue";
import { CircleClose } from "@element-plus/icons-vue";
import { Eptoast } from "@/utils/util.js";
const ImageAsideRef = ref(null);
const ImageMainRef = ref(null);
const handleOpenCreate = () => {
  ImageAsideRef.value.handelCreate();
};
const handleAsideChange = (image_class_id) =>
  ImageMainRef.value.loadData(image_class_id);

const handleOpenUpload = () => ImageMainRef.value.openUploadFile();
const dialogVisible = ref(false);

const callbackFunction = ref(null);

const open = (callback = null) => {
  callbackFunction.value = callback;
  dialogVisible.value = true;
};

const close = () => {
  dialogVisible.value = false;
};

const props = defineProps({
  modelValue: [String, Array],
  limit: {
    type: Number,
    default: 1,
  },
  preview: {
    type: Boolean,
    default: true,
  },
});

// 这样就可以修改modelValue 实现v-model的功能 双向绑定修改
const emit = defineEmits(["update:modelValue"]);

let urls = [];
const handleChoose = (e) => {
  urls = e.map((o) => o.url);
};

const submit = () => {
  let value = [];
  if (props.limit == 1) {
    value = urls[0];
  } else {
    // 老数组和新数组进行合并
    value = props.preview ? [...props.modelValue, ...urls] : [...urls];
    if (value.length > props.limit) {
      let limit = props.preview
        ? props.limit - props.modelValue.length
        : props.limit;

      return Eptoast("最多还能选择" + limit + "张");
    }
    if (value && props.preview) {
      emit("update:modelValue", value);
    }
    if (!props.preview && typeof callbackFunction.value === "function") {
      callbackFunction.value(value);
    }
  }

  // 关闭弹窗
  close();
};

const removeImage = (url) => {
  emit(
    "update:modelValue",
    props.modelValue.filter((u) => u != url)
  );
};

defineExpose({
  open,
});

// const removeImage = (url) =>
//   emit(
//     "update:modelValue",
//     props.modelValue.filter((u) => u != url)
//   );
</script>

<style lang="scss" scoped>
.image-header {
  border-bottom: 1px solid #eee;
}
.choose-image-btn {
  @apply w-[100px] h-[100px] rounded border flex justify-center items-center cursor-pointer hover:(bg-gray-100);
}
</style>
