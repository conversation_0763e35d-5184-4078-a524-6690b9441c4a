<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-28 15:36:59
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-11-11 10:40:42
 * @FilePath: /admin_vue3_element_plus/src/components/ListHeader.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="flex items-center justify-between mb-4">
    <div>
      <el-button
        v-if="btns.includes('create')"
        type="primary"
        size="small"
        @click="$emit('create')"
        >新增</el-button
      >

      <el-popconfirm
        v-if="btns.includes('delete')"
        title="是否删除选中记录?"
        confirm-button-text="确认"
        cancel-button-text="取消"
        @confirm.stop="$emit('delete')"
      >
        <template #reference>
          <el-button type="danger" size="small">批量删除</el-button>
        </template>
      </el-popconfirm>
      <slot />
    </div>

    <div>
      <el-tooltip
        v-if="btns.includes('refresh')"
        effect="dark"
        content="刷新数据"
        placement="top"
      >
        <el-button size="small" text @click="$emit('refresh')">
          <el-icon :size="15"><Refresh /></el-icon>
        </el-button>
      </el-tooltip>
      <el-tooltip
        v-if="btns.includes('download')"
        effect="dark"
        content="下载数据"
        placement="top"
      >
        <el-button size="small" text @click="$emit('download')">
          <el-icon :size="15"><Download /></el-icon>
        </el-button>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>
import { Download } from "@element-plus/icons-vue";
import { computed } from "vue";
const props = defineProps({
  layout: {
    type: String,
    default: "create,refresh",
  },
});

const btns = computed(() => props.layout.split(","));
defineEmits(["create", "refresh", "delete", "download"]);
</script>

<style lang="scss" scoped></style>
