<!--
 * @Author: yangy
 * @Date: 2023-12-27 16:49:57
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-29 11:47:57
 * @FilePath: /admin_vue3_element_plus/src/components/ImageMain.vue
 * @Description: 
 * 
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved. 
-->
<template>
  <el-main class="image-main">
    <div class="top py-3">
      <el-row :gutter="10">
        <el-col
          :span="6"
          :offset="0"
          v-for="(item, index) in list"
          :key="index"
        >
          <!-- 因为卡片组件内部有个内间距 消除这个内间距的方法是  :body-style="{padding:0}"-->
          <!-- preview-src-list: 可预览 -->
          <el-card
            shadow="hover"
            class="relative mb-3"
            :body-style="{ padding: 0 }"
            :class="{ 'border-blue-500': item.checked }"
          >
            <el-image
              :src="item.url"
              fit="cover"
              :lazy="true"
              class="w-full h-full"
              :preview-src-list="[item.url]"
              :initial-index="0"
            />

            <div class="image-title">{{ item.name }}</div>
            <div class="flex items-center justify-center p-2">
              <el-checkbox
                v-model="item.checked"
                @change="handleChooseChange(item)"
                v-if="openChoose"
              />
              <el-button
                type="primary"
                size="small"
                text
                @click="handleEdit(item)"
                >重命名</el-button
              >
              <el-popconfirm
                title="是否要删除该图片?"
                confirmButtonText="确认"
                cancel-button-text="取消"
                @confirm="handleDelete(item.id)"
              >
                <template #reference>
                  <el-button class="!m-0" type="primary" size="small" text
                    >删除</el-button
                  >
                </template>
              </el-popconfirm>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <div class="bottom">
      <el-pagination
        background
        layout="prev,pager,next"
        :total="total"
        v-model:current-page="currentPage"
        :page-size="limit"
        @current-change="getData"
      />
    </div>
  </el-main>
  <el-drawer v-model="drawer" title="上传图片">
    <UploadFile :data="{ image_class_id }" @success="handleUploadSuccess" />
  </el-drawer>
</template>

<script setup>
import { ref, computed } from "vue";
import { getImageList, updateImage, deleteImage } from "@/api/image.js";
import { showPrompt, Eptoast } from "@/utils/util.js";
import UploadFile from "./UploadFile.vue";
import { type } from "windicss/utils";
import { number } from "echarts";
// 上传图片
const drawer = ref(false);
const openUploadFile = () => (drawer.value = true);

const currentPage = ref(1);
const total = ref(0);
const limit = ref(10);
const list = ref([]);
const loading = ref(false);
const image_class_id = ref(0);

//获取数据
function getData(p = null) {
  if (typeof p == "number") {
    currentPage.value = p;
  }
  loading.value = true;
  getImageList(image_class_id.value, currentPage.value)
    .then((res) => {
      total.value = res.data.totalCount;
      list.value = res.data.list.map((o) => {
        o.checked = false;
        return o;
      });
    })
    .finally(() => {
      loading.value = false;
    });
}

//根据分类Id 重新加载图片列表
const loadData = (id) => {
  currentPage.value = 1;
  image_class_id.value = id;
  getData();
};
//重命名的方法
const handleEdit = (item) => {
  showPrompt("重命名", item.name)
    .then(({ value }) => {
      // confirm
      loading.value = true;
      updateImage(item.id, value)
        .then((res) => {
          Eptoast("更新成功");
          getData();
        })
        .finally(() => {
          loading.value = false;
        });
    })
    .catch(() => {
      // cancel
    });
};
const handleDelete = (id) => {
  loading.value = true;
  deleteImage([id])
    .then((res) => {
      Eptoast("删除成功!");
      getData();
    })
    .finally(() => {
      loading.value = false;
    });
};

// 上传成功
const handleUploadSuccess = () => getData(1);

// 不同场景的应用,有的需要CheckBox 有的不需要.
// 默认不需要CheckBox
const props = defineProps({
  openChoose: {
    type: Boolean,
    default: false,
  },
  limit: {
    type: Number,
    default: 1,
  },
});

//将选中的图片 回显
const emit = defineEmits(["choose"]);
// 箭头函数有无大括号的区别是 如果使用大括号则需要return 没有大括号则不用return
// const checkedImage = computed(()=>list.value.filter(o=>o.checked))

// 用计算属性 计算一共选择了几张图片,
const checkedImage = computed(() => {
  // 拿到checked为true的属性,并返回该对象
  return list.value.filter((o) => {
    return o.checked;
  });
});
const handleChooseChange = (item) => {
  // 只能选择 limit 张
  if (item.checked && checkedImage.value.length > props.limit) {
    // 如果选中的大于 limit 张则将该选择的置为未选择,并提示
    item.checked = false;
    return Eptoast(`最多只能选择${props.limit}张`, "error");
  }
  // 符合条件的, 只选了一张的情况
  // 将被选择的图片的值 传回到父组件
  // console.log(checkedImage.value);
  // 计算属性取值用 .value
  emit("choose", checkedImage.value);
};
defineExpose({
  loadData,
  openUploadFile,
});
</script>

<style scoped>
.image-main {
  border-right: 1px solid #eee;
  position: relative;
  @apply flex flex-col;
}

.image-main .top {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 50px;
  overflow-y: auto;
}

.image-main .bottom {
  position: absolute;
  bottom: 0;
  height: 50px;
  left: 0;
  right: 0;
  @apply flex items-center justify-center;
}

.image-title {
  position: absolute;
  bottom: 45px;
  left: 0px;
  right: 0px;
  @apply text-sm truncate text-gray-100 bg-opacity-50 bg-gray-800 px-2 py-1;
}
/* 以下为滚动条的样式 */

/* 滚动条的整体部分 限制宽高 */
::-webkit-scrollbar {
  width: 4px;
  height: 6px;
}
/* 滚动条的边角部分 滚动条的交汇处*/
::-webkit-scrollbar-corner {
  display: block;
}
/* 滚动条的小方块部分 */
::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.2);
}

/* 滚动条的轨道 */
::-webkit-scrollbar-thumb,
::-webkit-scrollbar-track {
  border-right-color: transparent;
  border-left-color: transparent;
  background-color: rgba(0, 0, 0, 0.1);
}
</style>
