<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-26 15:08:25
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-02 12:19:36
 * @FilePath: /admin_vue3_element_plus/src/components/UploadFile.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- drag 表示拖拽上传 -->
  <el-upload
    drag
    action="uploadImageAction"
    multiple
    :headers="{
      token,
    }"
    name="img"
    :data="data"
    :on-success="uploadSuccess"
    :on-error="uploadError"
  >
    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
    <div class="el-upload__text">
      Drop file here or <em>click to upload</em>
    </div>
    <template #tip>
      <div class="el-upload__tip">
        jpg/png files with a size less than 500kb
      </div>
    </template>
  </el-upload>
</template>

<script setup>
import { uploadImageAction } from "@/api/image.js";
import { getToken } from "@/utils/auth.js";
import { Eptoast } from "@/utils/util.js";
const token = getToken();
defineProps({
  data: Object,
});

const emit = defineEmits(["success"]);

const uploadSuccess = (response, uploadFile, uploadFiles) => {
  emit("success", {
    response,
    uploadFile,
    uploadFiles,
  });
};

const uploadError = (error, uploadFile, uploadFiles) => {
  let msg = JSON.parse(error.message).msg || "上传失败";
  Eptoast(msg, "error");
};
</script>
