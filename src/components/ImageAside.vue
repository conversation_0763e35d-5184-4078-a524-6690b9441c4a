<!--
 * @Author: yangy
 * @Date: 2023-12-27 16:48:59
 * @LastEditors: vinbrave <EMAIL>
 * @LastEditTime: 2024-02-28 21:56:14
 * @FilePath: /admin_vue3_element_plus/src/components/ImageAside.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by 青柠利合科技(北京)有限公司, All Rights Reserved. 
-->
<template>
  <el-aside width="220px" class="image-aside" v-loading="loading">
    <div class="top">
      <AsideList
        :active="activeId == item.id"
        v-for="(item, index) in list"
        :key="index"
        @edit="handleEdit(item)"
        @delete="handleDelete(item.id)"
        @click="handleChangeActivedId(item.id)"
        >{{ item.name }}</AsideList
      >
    </div>
    <div class="bottom">
      <el-pagination
        background
        layout="prev,next"
        :total="total"
        v-model:current-page="currentPage"
        :page-size="limit"
        @current-change="getData"
      />
    </div>
  </el-aside>

  <FormDrawer :title="drawerTitle" ref="formDrawerRef" @submit="handelSubmit">
    <el-form
      :model="form"
      ref="formRef"
      :rules="rules"
      label-width="80px"
      :inline="false"
    >
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>

      <el-form-item label="排序" prop="order">
        <el-input-number
          v-model="form.order"
          :min="0"
          :max="1000"
          @change="handleChange"
        />
      </el-form-item>
    </el-form>
  </FormDrawer>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
import FormDrawer from "./FormDrawer.vue";
import AsideList from "./AsideList.vue";
import {
  getImageClassList,
  createImageClass,
  updateImageClass,
  deleteImageClass,
} from "@/api/image_class.js";
import { Eptoast } from "@/utils/util.js";

// 加载动画
const loading = ref(false);
const list = ref([]);
const activeId = ref(0);

// 分页
const currentPage = ref(1); // 当前页
const total = ref(0); //总页数
const limit = ref(10); //
const formDrawerRef = ref(null);
const editId = ref(0);
const drawerTitle = computed(() => (editId.value ? "修改" : "新增"));

//获取数据 p是接收current-change传过来的当前页码
function getData(p = null) {
  if (typeof p == "number") {
    currentPage.value = p;
  }
  loading.value = true;
  // 获取数据的时候传当前页的页码
  getImageClassList(currentPage.value)
    .then((res) => {
      total.value = res.data.totalCount;
      list.value = res.data.list;
      // 刷新列表之后,默认让第一个处于激活状态
      let item = list.value[0];
      if (item) {
        // activeId.value = item.id
        handleChangeActivedId(item.id);
      }
    })
    .finally(() => {
      loading.value = false;
    });
}
getData();

const form = reactive({
  name: "",
  order: 50,
});
const rules = {
  name: [
    {
      required: true,
      message: "图库分类名称不能为空",
      trigger: "blur",
    },
  ],
};

const formRef = ref(null);
// 表单提交
const handelSubmit = () => {
  formRef.value.validate((valid) => {
    if (!valid) {
      return false;
    }
    formDrawerRef.value.showLoading();
    const fun = editId.value
      ? updateImageClass(editId.value, form)
      : createImageClass(form);
    fun
      .then((res) => {
        // console.log("提交成功");
        Eptoast(drawerTitle.value + "成功");
        // 修改更新刷新当前页,新增刷新第一页
        getData(editId.value ? currentPage.value : 1);
        formDrawerRef.value.close();
      })
      .finally(() => {
        formDrawerRef.value.hideLoading();
      });
  });
};

// 打开抽屉组件,新增
const handelCreate = () => {
  editId.value = 0;
  form.name = "";
  form.order = 50;
  formDrawerRef.value.open();
};
// 图库更改
const handleEdit = (row) => {
  editId.value = row.id;
  form.name = row.name;
  form.order = row.order;
  formDrawerRef.value.open();
};
//删除
const handleDelete = (id) => {
  loading.value = true;
  deleteImageClass(id)
    .then((res) => {
      Eptoast("删除成功");
      getData();
    })
    .finally(() => {
      loading.value = false;
    });
};
// 选中图库分类Id

const emit = defineEmits(["change"]);
//切换分类
function handleChangeActivedId(id) {
  activeId.value = id;
  emit("change", id);
}

defineExpose({
  handelCreate,
});
</script>

<style>
.image-aside {
  border-right: 1px solid #eee;
  position: relative;
  @apply flex flex-col;
}

.image-aside .top {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 50px;
  overflow-y: auto;
}

.image-aside .bottom {
  position: absolute;
  bottom: 0;
  height: 50px;
  left: 0;
  right: 0;
  @apply flex items-center justify-center;
}

/* 以下为滚动条的样式 */

/* 滚动条的整体部分 限制宽高 */
::-webkit-scrollbar {
  width: 4px;
  height: 6px;
}
/* 滚动条的边角部分 滚动条的交汇处*/
::-webkit-scrollbar-corner {
  display: block;
}
/* 滚动条的小方块部分 */
::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.2);
}

/* 滚动条的轨道 */
::-webkit-scrollbar-thumb,
::-webkit-scrollbar-track {
  border-right-color: transparent;
  border-left-color: transparent;
  background-color: rgba(0, 0, 0, 0.1);
}
</style>
