<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-03-29 14:49:03
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-03-29 16:10:22
 * @FilePath: /admin_vue3_element_plus/src/components/TagInput.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="flex gap-2">
    <!-- disable-transitions 是否禁用渐变动画 -->
    <el-tag
      v-for="tag in dynamicTags"
      :key="tag"
      closable
      :disable-transitions="false"
      @close="handleClose(tag)"
    >
      {{ tag }}
    </el-tag>
    <el-input
      v-if="inputVisible"
      ref="InputRef"
      v-model="inputValue"
      class="w-20"
      size="small"
      @keyup.enter="handleInputConfirm"
      @blur="handleInputConfirm"
    />
    <el-button v-else class="button-new-tag" size="small" @click="showInput">
      + 添加值
    </el-button>
  </div>
</template>

<script setup>
import { nextTick, ref } from "vue";
// 实现这种v-model的效果
// 由defineProps 来接收一个modelValue类型
const props = defineProps({
  modelValue: String,
});

// 一旦添加或者删除规格值,则需要通知父组件更新
// 涉及到通知父组件则需要 defineEmits()
const emit = defineEmits(["update:modelValue"]);
const inputValue = ref("");
const dynamicTags = ref(props.modelValue ? props.modelValue.split(",") : []);
const inputVisible = ref(false);
const InputRef = ref();

const handleClose = (tag) => {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1);
  // 通知父组件,将数据存到父组件的v-model中
  emit("update:modelValue", dynamicTags.value.join(","));
};

const showInput = () => {
  inputVisible.value = true;
  // Vue 3中的nextTick是一个全局函数，它返回一个Promise，可以用来在下次DOM更新循环结束之后执行一段逻辑
  nextTick(() => {
    InputRef.value.input.focus();
  });
};

const handleInputConfirm = () => {
  // 如果输入了数据,如果有值
  if (inputValue.value) {
    // 失去焦点或者按回车 则会向dynamicTags push一个值
    dynamicTags.value.push(inputValue.value);
    // 然后通知父组件,让父组件进行更新,是为了存到v-model中
    emit("update:modelValue", dynamicTags.value.join(","));
  }
  inputVisible.value = false;
  inputValue.value = "";
};
</script>

<style lang="scss" scoped>
.button-new-tag {
  margin-left: 10px;
}
</style>
