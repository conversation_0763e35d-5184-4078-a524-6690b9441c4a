<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-28 10:46:00
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-05-07 17:36:39
 * @FilePath: /admin_vue3_element_plus/src/components/ChooseImage.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog
    title="商品选择"
    v-model="dialogVisible"
    width="80%"
    destroy-on-close
  >
    <span>内容区域</span>
    <template #footer>
      <span>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import { getGoodsList } from "@/api/goods.js";
import { useInitTable } from "@/utils/useCommon.js";
const dialogVisible = ref(false);

const callbackFunction = ref(null);
const open = (callback = null) => {
  callbackFunction.value = callback;
  dialogVisible.value = true;
};

const close = () => {
  dialogVisible.value = false;
};

defineExpose({
  open,
});

const {
  handleSelectionChange,
  multipleTableRef,
  searchForm,
  tableData,
  loading,
  currentPage,
  total,
  limit,
  getData,
  multiSelectionIds,
} = useInitTable({
  searchForm: {
    title: "",
    tab: "all",
    category_id: null,
  },
  getList: getGoodsList,
  onGetListSuccess: (res) => {
    tableData.value = res.data.list;
    total.value = res.data.totalCount;
  },
});

const submit = () => {
  if (typeof callbackFunction.value === "function") {
    callbackFunction.value(multiSelectionIds.value);
  }
  // 关闭弹窗
  close();
};
</script>

<style lang="scss" scoped>
.choose-image-btn {
  @apply w-[100px] h-[100px] rounded border flex justify-center items-center cursor-pointer hover:(bg-gray-100);
}
</style>
