<!--
 * @Author: yangy
 * @Date: 2023-12-25 16:06:34
 * @LastEditors: yangy
 * @LastEditTime: 2023-12-25 16:50:21
 * @FilePath: /admin_vue3_element_plus/src/components/IndexNavs.vue
 * @Description: 
 * 
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved. 
-->
<template>
  <el-row :gutter="20" class="mt-5">
    <el-col :span="3"  v-for="(item) in iconNavs" :key="item.title">
      <el-card shadow="hover" @click="$router.push(item.path)">
       <div class="flex flex-col items-center justify-center cursor-pointer">
        <el-icon size="16" :class="item.color">
          <component :is="item.icon"></component>
        </el-icon>
        <span class="text-sm mt-2">{{ item.title }}</span>
       </div>
        <!-- card body -->
      </el-card>
      
    </el-col>
  </el-row>
</template>
  
<script setup>

const iconNavs = [
  {
    icon:"user",
    color:"text-light-blue-500",
    title:"用户",
    path:"/user/list"
  },
  {
    icon:"shopping-cart",
    color:"text-light-blue-500",
    title:"商品",
    path:"/goods/list"
  },
  {
    icon:"tickets",
    color:"text-fuchsia-500",
    title:"订单",
    path:"/order/list"
  },
  {
    icon:"comment",
    color:"text-teal-500",
    title:"评价",
    path:"/comment/list"
  },
  {
    icon:"picture",
    color:"text-rose-500",
    title:"图库",
    path:"/image/list"
  },
  {
    icon:"bell",
    color:"text-green-500",
    title:"公告",
    path:"/notice/list"
  },
  {
    icon:"set-up",
    color:"text-gray-500",
    title:"配置",
    path:"/setting/base"
  },
  {
    icon:"files",
    color:"text-yellow-500",
    title:"优惠券",
    path:"/coupon/list"
  }
]
</script>
  