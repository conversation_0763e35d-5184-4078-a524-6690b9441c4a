<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-05-29 11:04:22
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-05-29 12:49:05
 * @FilePath: /admin_vue3_element_plus/src/components/Search.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-form :model="model" label-width="80px" class="mb-3">
    <el-row :gutter="20">
      <slot />
      <template v-if="showSearch">
        <slot name="show" />
      </template>

      <el-col :span="8" :offset="showSearch ? 0 : 8">
        <div class="flex items-center justify-end">
          <el-button type="primary" @click="$emit('search')">搜索</el-button>
          <el-button @click="$emit('reset')">重置</el-button>
          <el-button
            type="primary"
            text
            @click="showSearch = !showSearch"
            v-if="hasShowSearch"
          >
            {{ showSearch ? "收起" : "展开" }}
            <el-icon>
              <ArrowUp v-if="showSearch" />
              <ArrowDown v-else />
            </el-icon>
          </el-button>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { ref, useSlots } from "vue";
defineProps({
  model: Object,
});
// 重置和搜索的两个功能需要暴露出去,以供外部调用
defineEmits(["search", "reset"]);
const showSearch = ref(false);
const slots = useSlots();
// 这两个 !! 是将某值强行转为布尔值
const hasShowSearch = ref(!!slots.show);
</script>
