<!--
 * @Author: yangy
 * @Date: 2023-12-19 16:32:20
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-12 16:29:36
 * @FilePath: /admin_vue3_element_plus/src/components/FormDrawer.vue
 * @Description: 
 * 
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved. 
-->
<template>
  <el-drawer
    v-model="showDrawer"
    :title="title"
    :size="size"
    :close-on-click-modal="false"
    :destroy-on-close="destoryOnClose"
  >
    <div class="formDrawer">
      <div class="body">
        <slot></slot>
      </div>
      <div class="actions">
        <el-button type="" @click="submit" :loading="loading">{{
          confirmText
        }}</el-button>
        <el-button type="default" @click="close">取 消</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref } from "vue";
const showDrawer = ref(false);
const props = defineProps({
  title: String,
  size: {
    type: String,
    default: "45%",
  },
  // 控制是否在关闭Drawer之后将子元素全部销毁
  destoryOnClose: {
    type: Boolean,
    default: false,
  },
  confirmText: {
    type: String,
    default: "提交",
  },
});

const loading = ref(false);
const showLoading = () => {
  loading.value = true;
};
const hideLoading = () => {
  loading.value = false;
};

// 打开抽屉
const open = () => {
  showDrawer.value = true;
};
// 关闭
const close = () => {
  showDrawer.value = false;
};
//提交  对外暴露事件  向父组件暴露事件
// vue文档中的  单文件组件==><script setup> ==> defineProps和defineEmits
// 只要我们点击了 template中定义的submit click事件  submit再去触发 emit通知事件(submit)
const emit = defineEmits(["submit"]);
const submit = () => emit("submit");
// 向父组件暴露
defineExpose({
  open,
  close,
  showLoading,
  hideLoading,
});
</script>

<style>
.formDrawer {
  width: 100%;
  height: 100%;
  position: relative;
  @apply flex flex-col;
}
.formDrawer .body {
  flex: 1;
  top: 0;
  left: 0;
  right: 0;
  bottom: 50px;
  overflow-y: auto;
  position: absolute;
}
.formDrawer .actions {
  height: 50px;
  @apply mt-auto flex items-center;
}
</style>
