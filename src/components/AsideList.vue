<!--
 * @Author: yangy
 * @Date: 2024-01-02 16:05:15
 * @LastEditors: yangy <EMAIL>
 * @LastEditTime: 2024-08-28 12:46:10
 * @FilePath: /admin_vue3_element_plus/src/components/AsideList.vue
 * @Description: 
 * 
 * Copyright (c) 2024 by 青柠利合科技(北京)有限公司, All Rights Reserved. 
-->
<template>
  <div class="aside-list" :class="{ active: active }">
    <span class="truncate"><slot /></span>
    <!-- click.stop 就是防止冒泡事件,防止点击分类的时候自动切换到分类下的图片 -->
    <el-button
      class="ml-auto px-1"
      text
      type="primary"
      size="small"
      @click.stop="$emit('edit')"
    >
      <el-icon :size="12"><Edit /></el-icon>
    </el-button>
    <!-- 直接在el-popconfirm 标签内使用click.stop 不管用 故在外面加个span标签 在里面的click加上stop然后有个空函数 -->
    <span @click.stop="() => {}">
      <el-popconfirm
        title="是否要删除该分类?"
        confirm-button-text="确认"
        cancel-button-text="取消"
        @confirm.stop="$emit('delete')"
      >
        <template #reference>
          <el-button text class="px-1" type="primary" size="small">
            <el-icon :size="12"><Close /></el-icon>
          </el-button>
        </template>
      </el-popconfirm>
    </span>
  </div>
</template>

<script setup>
defineProps({
  active: {
    type: Boolean,
    default: false,
  },
});

defineEmits(["edit", "delete"]);
</script>

<style>
.aside-list {
  border-bottom: 1px solid #f4f4f4;
  cursor: pointer;
  @apply flex items-center p-3 text-sm text-gray-600;
}

.aside-list:hover,
.active {
  @apply bg-blue-50;
}
</style>
