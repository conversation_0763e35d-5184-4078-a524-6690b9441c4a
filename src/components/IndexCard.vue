<!--
 * @Author: yangy
 * @Date: 2023-12-26 14:52:47
 * @LastEditors: yangy
 * @LastEditTime: 2023-12-26 15:46:35
 * @FilePath: /admin_vue3_element_plus/src/components/IndexCard.vue
 * @Description: 
 * 
 * Copyright (c) 2023 by 青柠利合科技(北京)有限公司, All Rights Reserved. 
-->
<template>
  <el-card shadow="never">
    <template #header>
    <div class="flex justify-between">
      <span class="text-sm">{{ title }}</span>
      <el-tag type="danger" effect="plain">{{ tip }}</el-tag>
    </div>
    </template>
    <!-- card body -->
    <el-row :gutter="20">
      <el-col :span="6" :offset="0" v-for="(item,index) in btns" :key=index>
        <el-card shadow="hover" class="border-0 bg-light-400">
         <div class="flex flex-col items-center justify-center">
          <span class="text-xl mb-2">{{ item.value }}</span>
          <span class="text-xs text-gray-500">{{ item.label }}</span>
         </div>
        </el-card>
      </el-col>
    </el-row>
    
  </el-card>
  
</template>
  
<script setup>
  
defineProps({
  title:String,
  tip:String,
  btns:Array,
})
</script>
  
  