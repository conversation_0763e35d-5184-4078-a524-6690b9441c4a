<!--
 * @Author: yangy <EMAIL>
 * @Date: 2024-02-29 16:13:58
 * @LastEditors: vinbrave <EMAIL>
 * @LastEditTime: 2024-02-29 21:48:41
 * @FilePath: /admin_vue3_element_plus/src/components/IconSelect.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="flex items-center">
    <el-icon :size="20" v-if="modelValue" class="mr-2">
      <component :is="modelValue"></component>
    </el-icon>
    <!-- filterable  可筛选 -->
    <el-select
      filterable
      :modelValue="modelValue"
      class="m-2"
      placeholder="选择图标"
      @change="handleChange"
    >
      <el-option v-for="item in icons" :key="item" :label="item" :value="item">
        <div class="flex items-center justify-between">
          <el-icon>
            <component :is="item"></component>
          </el-icon>
          <span class="text-gray-500">{{ item }}</span>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script setup>
import { ref } from "vue";
import * as iconList from "@element-plus/icons-vue";
defineProps({ modelValue: String });
const icons = ref(Object.keys(iconList));

const emit = defineEmits(["update:modelValue"]);
const handleChange = (icon) => {
  emit("update:modelValue", icon);
};
</script>

<style lang="stylus" scoped></style>
